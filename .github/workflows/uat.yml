name: NODEJS DEPLOY DEVELOP

on:
  pull_request:
    branches:
      - uat
    types: [closed]

jobs:
# วิเคราะห์คุณภาพโค้ด (Sonarqube) ------------------------------------------------------------------------------------------------
  sonarqube_scan:
    name: "วิเคราะห์คุณภาพโค้ด (Sonarqube)"
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: "วิเคราะห์โค้ดด้วยเครื่องมือ SonarQube"
      uses: sonarsource/sonarqube-scan-action@v4
      env:
        SONAR_TOKEN: ${{ vars.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

# สร้างบิลด์และสแกนความปลอดภัย ---------------------------------------------------------------------------------------------------
  security_scan:
    name: "สร้างบิ้วด์และสแกนความปลอดภัย"
    runs-on: ubuntu-latest

    steps:
      - name: "นำโค้ดออกมา"
        uses: actions/checkout@v4

      - name: "ติดตั้ง Nodejs"
        uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}

      - name: "ติดตั้ง dependencies"
        run: npm install

      - name: "ตรวจสอบช่องโหว่ด้านความปลอดภัย (security vulnerabilities)"
        run: npm audit --audit-level=high

# สร้างบิลด์และนำไป Deploy ขึ้นเครื่อง Develop ----------------------------------------------------------------------------------------------
  build_and_deploy:
    name: "สร้างบิลด์และนำไป Deploy ขึ้นเครื่อง Develop"
    runs-on: ubuntu-latest
    steps:
      - name: "นำโค้ดออกมา"
        uses: actions/checkout@v4

# การแก้ไขค่าตั้งค่า (Configuration) -------------------------------------------------------------------------------------------------------
#      - name: "การแก้ไขค่าตั้งค่า (Configuration)"
#        run: |
#          sed -i 's|http://localhost|https://test-dev.buu.ac.th|g' ${{ github.workspace }}/config/app.php

# คัดลอกไฟล์ไปที่ Develop server ---------------------------------------------------------------------------------------------------------
      - name: "ติดตั้ง dependencies"
        run: |
          npm install

      - name: "คัดลอกไฟล์ไปที่ Develop server"
        uses: appleboy/scp-action@v1
        with:
          host: ${{ vars.SSH_DEVELOP }}
          username: develop
          port: 22
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "*,!git/*"
          target: /home/<USER>/${{ vars.PROJECT_NAME }}/public_html

# ติดตั้งแอปพลิเคชันบน Develop server -----------------------------------------------------------------------------------------------------
      - name: "ติดตั้งแอปพลิเคชันบน Develop server"
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ vars.SSH_DEVELOP }}
          username: develop
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cp /home/<USER>/k3s-temp-back/* /home/<USER>/${{ vars.PROJECT_NAME }}
            cd /home/<USER>/${{ vars.PROJECT_NAME }}
            export KUBECONFIG=~/.kube/config
            sed -i 's|K8S_PROJECT_NAME|${{ vars.PROJECT_NAME }}|g' deployment-k3s.yml
            sed -i 's|K8S_NODE_VERSION|${{ vars.NODE_VERSION }}|g' deployment-k3s.yml
            sed -i 's|K8S_PROJECT_NAME|${{ vars.PROJECT_NAME }}|g' service-k3s.yml
            sed -i 's|K8S_PROJECT_NAME|${{ vars.PROJECT_NAME }}|g' ingress-k3s.yml
            kubectl apply -f deployment-k3s.yml
            kubectl apply -f service-k3s.yml
            kubectl apply -f ingress-k3s.yml
            rm -f deployment-k3s.yml
            rm -f service-k3s.yml
            rm -f ingress-k3s.yml
            kubectl rollout restart deployment ${{ vars.PROJECT_NAME }} -n default