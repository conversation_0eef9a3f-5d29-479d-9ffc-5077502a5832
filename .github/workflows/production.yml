name: NODEJS DEPLOY PRODUCTION

on:
  pull_request:
    branches:
      - main
    types: [closed]

jobs:
# สร้างบิลด์และนำไป Deploy ขึ้นเครื่อง Production ----------------------------------------------------------------------------------------------
  build_and_deploy:
    name: "สร้างบิลด์และนำไป Deploy ขึ้นเครื่อง Production"
    runs-on: ubuntu-latest
    steps:
      - name: "นำโค้ดออกมา"
        uses: actions/checkout@v4

# การแก้ไขค่าตั้งค่า (Configuration) -------------------------------------------------------------------------------------------------------
#      - name: "การแก้ไขค่าตั้งค่า (Configuration)"
#        run: |
#          sed -i 's|http://localhost|https://test-dev.buu.ac.th|g' ${{ github.workspace }}/config/app.php

# คัดลอกไฟล์ไปที่ Production server ---------------------------------------------------------------------------------------------------------
      - name: "ติดตั้ง dependencies"
        run: |
          npm install

      - name: "คัดลอกไฟล์ไปที่ Production server"
        uses: appleboy/scp-action@v1
        with:
          host: ${{ vars.SSH_PRODUCTION }}
          username: develop
          port: 22
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "*,!git/*"
          target: /home/<USER>/web-${{ vars.PROJECT_NAME }}/public_html
