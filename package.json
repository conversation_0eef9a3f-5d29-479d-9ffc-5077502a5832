{"name": "intern-cc-quasar", "version": "0.0.1", "description": "A Quasar Project", "productName": "Intern CC", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "cypress:open": "cypress open", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"@quasar/extras": "^1.17.0", "@quasar/quasar-ui-qmarkdown": "^2.0.5", "@vueup/vue-quill": "^1.2.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "crypto-js": "^4.2.0", "cypress": "^14.5.1", "jwt-decode": "^4.0.0", "pinia": "^3.0.3", "quasar": "^2.18.1", "quill": "^2.0.3", "vue": "^3.5.17", "vue-chartjs": "^5.3.2", "vue-i18n": "^11.1.9", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@4tw/cypress-drag-drop": "^2.3.0", "@eslint/js": "^9.31.0", "@intlify/unplugin-vue-i18n": "^6.0.8", "@quasar/app-vite": "^2.2.1", "@quasar/quasar-app-extension-qcalendar": "^4.1.2", "@types/crypto-js": "^4.2.2", "@types/node": "^22.16.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "autoprefixer": "^10.4.21", "cypress-file-upload": "^5.0.8", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "prettier": "^3.6.2", "typescript": "^5.8.3", "vite-plugin-checker": "^0.9.3", "vue-tsc": "^2.2.12"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}