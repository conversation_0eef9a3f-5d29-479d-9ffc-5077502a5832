import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { UtilsService } from 'src/utils/utils.service';
import { IS_PUBLIC_KEY } from 'src/common/decorators/public.decorator';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly utilsService: UtilsService,
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    const encryptionKey =
      this.configService.get<string>('ENCRYPTION_KEY') ||
      'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';

    if (!token) {
      throw new UnauthorizedException();
    }
    try {
      const payload = await this.jwtService.verifyAsync(token);
      console.log('AuthGuard: Token payload:', payload);
      console.log('AuthGuard: Using encryption key:', encryptionKey);
      console.log('AuthGuard: Encrypted payload sub:', payload.sub);
      
      const decryptedUser = this.utilsService.decryptObject(
        encryptionKey,
        payload.sub,
      );
      
      console.log('AuthGuard: Decrypted user:', decryptedUser);

      if (!decryptedUser) {
        console.log('AuthGuard: Decryption failed - user is null');
        throw new UnauthorizedException('Invalid token');
      }

      request['user'] = decryptedUser;
      console.log('AuthGuard: User set in request successfully');
    } catch (error) {
      console.log('AuthGuard: Error occurred:', error.message);
      throw new UnauthorizedException();
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
