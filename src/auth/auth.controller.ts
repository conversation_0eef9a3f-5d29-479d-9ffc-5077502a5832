import { Body, Controller, Post, HttpCode, HttpStatus } from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post('login')
  @ApiOperation({ summary: 'Simple login with username and password' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string', example: 'superadmin' },
        password: { type: 'string', example: '1234' },
      },
      required: ['username', 'password'],
    },
  })
  simpleSignIn(
    @Body('username') username: string,
    @Body('password') password: string,
  ) {
    return this.authService.simpleSignIn(username, password);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post('loginBuu')
  @ApiOperation({ summary: 'Legacy encrypted login (paused for now)' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string' },
        password: { type: 'string' },
      },
    },
  })
  signIn(
    @Body('username') username: string,
    @Body('password') password: string,
  ) {
    // Temporarily disable the encrypted login
    throw new Error(
      'Legacy encrypted login is temporarily disabled. Please use /auth/login endpoint.',
    );
  }
}
