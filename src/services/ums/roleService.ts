import type { QTableProps } from 'quasar';
import { api } from 'src/boot/axios';
import type { DataResponse } from 'src/types/data';
import type { Role } from 'src/types/models';
import { formatParams } from 'src/utils/utils';

export class RoleService {
  private static path = 'roles';

  static createRole(dto: Role) {
    return api.post<Role>(`${this.path}`, dto);
  }

  static getRoleById(id: number) {
    return api.get<Role>(`${this.path}/${id}`);
  }

  static updateRole(id: number, data: Partial<Role>) {
    return api.patch<Role>(`${this.path}/${id}`, data);
  }

  static deleteRole(id: number) {
    return api.delete<Role>(`${this.path}/${id}`);
  }

  static async getRoles(pagination: QTableProps['pagination']) {
    const params = formatParams(pagination);
    const res = await api.get<DataResponse<Role>>(`${this.path}`, { params });
    return res.data;
  }
}
