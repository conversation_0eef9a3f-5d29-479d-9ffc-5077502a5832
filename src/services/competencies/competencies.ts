import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import { Notify } from 'quasar';
import type { Competency } from 'src/types/models';
import type { DataResponse, CompetencyQueryParams } from 'src/types/data';
import { formatParams } from 'src/utils/utils';

const showError = (message: string) => {
  Notify.create({
    type: 'negative',
    message,
    position: 'bottom',
    timeout: 3000,
  });
};

export class CompetenciesService {
  private path = '/competencies';
  comp_type = 'สมรรถนะหลัก';
  career_type = 'วิชาการ';

  constructor(type?: string) {
    if (type) {
      this.comp_type = type;
    }
  }

  async getAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const format = formatParams(pag, search);
      const params = {
        ...format,
        comp_type: this.comp_type,
        career_type: this.career_type
      } as CompetencyQueryParams;

      const res = await axios.get<DataResponse<Competency>>(this.path, { params });
      return res.data;
    } catch {
      showError('ดึงข้อมูลสมรรถนะทั้งหมดล้มเหลว');
      throw new Error('Get all Competencies failed');
    }
  }

  async fetchOne(id: number): Promise<Competency> {
    try {
      const res = await axios.get<Competency>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ดึงข้อมูลสมรรถนะล้มเหลว');
      throw new Error('Get Competency failed');
    }
  }

  async create(data: Competency): Promise<Competency> {
    try {
      const res = await axios.post<Competency>(this.path, data);
      return res.data;
    } catch {
      showError('สร้างสมรรถนะไม่สำเร็จ');
      throw new Error('Create Competency failed');
    }
  }

  async update(id: number, data: Partial<Competency>) {
    try {
      const res = await axios.patch<Competency>(`${this.path}/${id}`, data);
      return res.data;
    } catch {
      showError('อัปเดตสมรรถนะล้มเหลว');
      throw new Error('Update Competency failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
    } catch {
      showError('ลบสมรรถนะล้มเหลว');
      throw new Error('Remove Competency failed');
    }
  }
}
