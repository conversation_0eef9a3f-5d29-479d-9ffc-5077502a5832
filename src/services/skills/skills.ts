import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import { Notify } from 'quasar';
import type { Skill } from 'src/types/models';
import type { DataResponse, SkillQueryParams } from 'src/types/data';
import { formatParams } from 'src/utils/utils';
import { toRaw } from 'vue';

const showError = (message: string) => {
  Notify.create({
    type: 'negative',
    message,
    position: 'bottom',
    timeout: 3000,
  });
};

export class SkillsService {
  private path = '/skills';
  career_type = 'ความรู้และทักษะทั่วไปของบุคลากร';
  constructor(type: string) {
    this.career_type = type;
  }

  async getAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const format = formatParams(pag, search);
      const params = { ...format, career_type: this.career_type } as SkillQueryParams;
      const res = await axios.get<DataResponse<Skill>>(this.path, { params });
      return res.data;
    } catch {
      showError('ดึงข้อมูลทักษะทั้งหมดล้มเหลว');
      throw new Error('Get all Skills failed');
    }
  }

  async fecthOne(id: number): Promise<Skill> {
    try {
      const res = await axios.get<Skill>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ดึงข้อมูลทักษะล้มเหลว');
      throw new Error('Get all Skills failed');
    }
  }

  async create(data: Skill): Promise<Skill> {
    try {
      const payload = {
        ...data,
        competencyIds: toRaw(data.competencyIds),
      };
      console.log('payload', payload);

      const res = await axios.post<Skill>(this.path, payload);
      return res.data;
    } catch {
      showError('สร้างทักษะไม่สำเร็จ');
      throw new Error('Create summary failed');
    }
  }

  async update(id: number, params: Partial<Skill>) {
    try {
      // ลบฟิลด์ที่ไม่ต้องการ
      delete params.evaluator;
      delete params.evaluatorId;
      delete params.program;
      delete params.id;
      delete params.competencies;

      const res = await axios.patch<Skill>(`${this.path}/${id}`, params);
      return res.data;
    } catch {
      showError('อัปเดตทักษะล้มเหลว');
      throw new Error('Update summary failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
    } catch {
      showError('ลบทักษะล้มเหลว');
      throw new Error('Remove skill failed');
    }
  }
}
