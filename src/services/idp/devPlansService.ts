import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import { Notify } from 'quasar';
import type { DevelopmentPlan } from 'src/types/idp';

const showError = (message: string) => {
  Notify.create({
    type: 'negative',
    message,
    position: 'bottom',
    timeout: 3000,
  });
};

export interface DevelopmentPlanResponse {
  data: DevelopmentPlan[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface DevelopmentPlanQueryParams {
  limit: number;
  page: number;
  sortBy: string;
  order: 'ASC' | 'DESC';
  isCentral: 'central' | 'department';
  search?: string;
}

export class DevPlansService {
  private path = '/development-plans';

  async getAll(
    pag: QTableProps['pagination'],
    search?: string,
    isCentral: 'central' | 'department' = 'central',
  ): Promise<DevelopmentPlanResponse> {
    try {
      const params: DevelopmentPlanQueryParams = {
        limit: pag?.rowsPerPage || 10,
        page: pag?.page || 1,
        sortBy: pag?.sortBy || 'id',
        order: pag?.descending ? 'DESC' : 'ASC',
        isCentral,
        ...(search && { search }),
      };

      const res = await axios.get<DevelopmentPlanResponse>(this.path, { params });
      return res.data;
    } catch (error) {
      console.error('Error fetching development plans:', error);
      showError('ไม่สามารถโหลดข้อมูลแผนพัฒนาได้');
      throw new Error('Get all development plans failed');
    }
  }

  async fetchOne(id: number): Promise<DevelopmentPlan> {
    try {
      const res = await axios.get<DevelopmentPlan>(`${this.path}/${id}`);
      return res.data;
    } catch (error) {
      console.error('Error fetching development plan:', error);
      showError('ไม่สามารถโหลดข้อมูลแผนพัฒนาได้');
      throw new Error('Get development plan failed');
    }
  }

  async create(data: Omit<DevelopmentPlan, 'id'>): Promise<DevelopmentPlan> {
    try {
      const res = await axios.post<DevelopmentPlan>(this.path, data);
      Notify.create({
        type: 'positive',
        message: 'สร้างแผนพัฒนาสำเร็จ',
        position: 'bottom',
        timeout: 3000,
      });
      return res.data;
    } catch (error) {
      console.error('Error creating development plan:', error);
      showError('สร้างแผนพัฒนาไม่สำเร็จ');
      throw new Error('Create development plan failed');
    }
  }

  async update(id: number, data: Partial<DevelopmentPlan>): Promise<DevelopmentPlan> {
    try {
      const res = await axios.patch<DevelopmentPlan>(`${this.path}/${id}`, data);
      Notify.create({
        type: 'positive',
        message: 'อัปเดตแผนพัฒนาสำเร็จ',
        position: 'bottom',
        timeout: 3000,
      });
      return res.data;
    } catch (error) {
      console.error('Error updating development plan:', error);
      showError('อัปเดตแผนพัฒนาไม่สำเร็จ');
      throw new Error('Update development plan failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
      Notify.create({
        type: 'positive',
        message: 'ลบแผนพัฒนาสำเร็จ',
        position: 'bottom',
        timeout: 3000,
      });
    } catch (error) {
      console.error('Error deleting development plan:', error);
      showError('ลบแผนพัฒนาไม่สำเร็จ');
      throw new Error('Remove development plan failed');
    }
  }
}
