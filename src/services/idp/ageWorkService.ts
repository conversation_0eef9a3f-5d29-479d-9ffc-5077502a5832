import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import { Notify } from 'quasar';
import type { AgeWork } from 'src/types/idp';
import type { DataResponse } from 'src/types/data';
import { formatParams } from 'src/utils/utils';

const showError = (message: string) => {
  Notify.create({
    type: 'negative',
    message,
    position: 'bottom',
    timeout: 3000,
  });
};

export class AgeWorkService {
  private path = '/age-works';

  async getAgeWorksByCriteria(criteriaId: number, pag: QTableProps['pagination'], search?: string) {
    try {
      const params = formatParams(pag, search);
      const res = await axios.get<DataResponse<AgeWork>>(
        `/age-work-criteria/${criteriaId}/age-works`,
        { params },
      );
      return res.data;
    } catch (error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      showError((error as any)?.response?.data?.message || 'ดึงข้อมูลอายุงานตามเกณฑ์ล้มเหลว');
      throw new Error('Get AgeWorks by criteria failed');
    }
  }

  async fetchOne(id: number): Promise<AgeWork> {
    try {
      const res = await axios.get<AgeWork>(`${this.path}/${id}`);
      return res.data;
    } catch (error: unknown) {
      console.error(
        'Get age work error details:',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (error as any)?.response?.data || error,
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errorMessage = (error as any)?.response?.data?.message || 'ดึงข้อมูลอายุงานล้มเหลว';
      showError(errorMessage);
      throw new Error('Get AgeWork failed');
    }
  }

  async create(data: Omit<AgeWork, 'id'>): Promise<AgeWork> {
    try {
      const res = await axios.post<AgeWork>(this.path, data);
      return res.data;
    } catch (error: unknown) {
      console.error(
        'Create age work error details:',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (error as any)?.response?.data || error,
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errorMessage = (error as any)?.response?.data?.message || 'สร้างอายุงานไม่สำเร็จ';
      showError(errorMessage);
      throw new Error('Create AgeWork failed');
    }
  }

  async update(id: number, data: Partial<AgeWork>) {
    try {
      const res = await axios.patch<AgeWork>(`${this.path}/${id}`, data);
      return res.data;
    } catch (error: unknown) {
      console.error(
        'Update age work error details:',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (error as any)?.response?.data || error,
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errorMessage = (error as any)?.response?.data?.message || 'อัปเดตอายุงานล้มเหลว';
      showError(errorMessage);
      throw new Error('Update AgeWork failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
    } catch (error: unknown) {
      console.error(
        'Remove age work error details:',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (error as any)?.response?.data || error,
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errorMessage = (error as any)?.response?.data?.message || 'ลบอายุงานล้มเหลว';
      showError(errorMessage);
      throw new Error('Remove AgeWork failed');
    }
  }
}
