import { api as axios } from 'src/boot/axios';
import type { QTableProps } from 'quasar';
import { Notify } from 'quasar';
import type { AgeWorkCriteria, AgeWork } from 'src/types/idp';
import type { DataResponse } from 'src/types/data';
import { formatParams } from 'src/utils/utils';

const showError = (message: string) => {
  Notify.create({
    type: 'negative',
    message,
    position: 'bottom',
    timeout: 3000,
  });
};

export class AgeWorkCriteriaService {
  private path = '/age-work-criteria';

  async getAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const params = formatParams(pag, search);
      const res = await axios.get<DataResponse<AgeWorkCriteria>>(this.path, { params });
      return res.data;
    } catch {
      showError('ดึงข้อมูลเกณฑ์อายุงานทั้งหมดล้มเหลว');
      throw new Error('Get all AgeWorkCriteria failed');
    }
  }

  async fetchOne(id: number): Promise<AgeWorkCriteria> {
    try {
      const res = await axios.get<AgeWorkCriteria>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ดึงข้อมูลเกณฑ์อายุงานล้มเหลว');
      throw new Error('Get AgeWorkCriteria failed');
    }
  }

  async create(data: Omit<AgeWorkCriteria, 'id'>): Promise<AgeWorkCriteria> {
    try {
      const res = await axios.post<AgeWorkCriteria>(this.path, data);
      return res.data;
    } catch (error: unknown) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      console.error('Create error details:', (error as any)?.response?.data || error);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errorMessage = (error as any)?.response?.data?.message || 'สร้างเกณฑ์อายุงานไม่สำเร็จ';
      showError(errorMessage);
      throw new Error('Create AgeWorkCriteria failed');
    }
  }

  async update(id: number, data: Partial<AgeWorkCriteria>) {
    try {
      const res = await axios.patch<AgeWorkCriteria>(`${this.path}/${id}`, data);
      return res.data;
    } catch (error: unknown) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      console.error('Update error details:', (error as any)?.response?.data || error);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errorMessage = (error as any)?.response?.data?.message || 'อัปเดตเกณฑ์อายุงานล้มเหลว';
      showError(errorMessage);
      throw new Error('Update AgeWorkCriteria failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
    } catch {
      showError('ลบเกณฑ์อายุงานล้มเหลว');
      throw new Error('Remove AgeWorkCriteria failed');
    }
  }

  async getAgeWorksByCriteria(id: number, pag: QTableProps['pagination'], search?: string) {
    try {
      const params = formatParams(pag, search);
      const res = await axios.get<DataResponse<AgeWork>>(`${this.path}/${id}/age-works`, {
        params,
      });
      return res.data;
    } catch {
      showError('ดึงข้อมูลอายุงานตามเกณฑ์ล้มเหลว');
      throw new Error('Get AgeWorks by criteria failed');
    }
  }
}
