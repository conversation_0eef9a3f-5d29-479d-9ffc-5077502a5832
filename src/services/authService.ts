import { api as http } from 'src/boot/axios';
import { Notify } from 'quasar';

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

async function loginBuu(username: string, password: string) {
  try {
    return await http.post('/auth/loginBuu', {
      username,
      password,
    });
  } catch {
    showError('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
    throw new Error('Login failed');
  }
}

async function login(username: string, password: string) {
  try {
    return await http.post('/auth/login', {
      username,
      password,
    });
  } catch {
    showError('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
    throw new Error('Lo<PERSON> failed');
  }
}

export default {
  loginBuu,
  login,
};
