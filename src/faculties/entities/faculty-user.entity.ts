import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from 'src/resources/users/entities/user.entity';
import { Faculty } from './faculty.entity';
import { FacultyUserRoles } from './faculty-user-role.entity';

@Entity('HRD_FACULTY_USERS')
export class FacultyUsers {
  @PrimaryGeneratedColumn({ name: 'FU_ID' })
  id: number;

  @Column({ name: 'FAC_ID', nullable: false })
  facultyId: number;

  @Column({ name: 'USR_ID', nullable: false })
  userId: number;

  @ManyToOne(() => Faculty, (faculty) => faculty.facultyUsers)
  @JoinColumn({ name: 'FAC_ID' })
  faculty: Faculty;

  @ManyToOne(() => User, (user) => user.facultyUsers)
  @JoinColumn({ name: 'USR_ID' })
  users: User;

  @OneToMany(
    () => FacultyUserRoles,
    (facultyRoles) => facultyRoles.facultyUser,
  )
  facultyUserRoles: FacultyUserRoles[];

  // One to many faculty user role
}
