import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { DevelopmentPlan } from 'src/resources/individual-develop-plans/development-plans/entities/development-plan.entity';
import { FacultyUsers } from './faculty-user.entity';

@Entity('HRD_FACULTIES')
export class Faculty {
  @PrimaryGeneratedColumn({ name: 'FAC_ID' })
  id: number;

  @Column({ name: 'FAC_NAME_TH', nullable: false })
  nameTh: string;

  @Column({ name: 'FAC_NAME_EN', nullable: false })
  nameEn: string;

  @OneToMany(() => FacultyUsers, (facultyUser) => facultyUser.faculty, {
    cascade: true,
  })
  facultyUsers: FacultyUsers[];

  @OneToMany(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.faculty,
  )
  developmentPlans: DevelopmentPlan[];
}
