import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Role } from 'src/resources/roles/entities/role.entity';
import { FacultyUsers } from './faculty-user.entity';

@Entity('HRD_FACULTY_USER_ROLES')
export class FacultyUserRoles {
  @PrimaryGeneratedColumn({ name: 'FUR_ID' })
  id: number;

  @Column({ name: 'FU_ID', nullable: false })
  facultyUserId: number;

  @Column({ name: 'ROL_ID', nullable: false })
  roleId: number;

  @ManyToOne(() => FacultyUsers, (fu) => fu.facultyUserRoles)
  @JoinColumn({ name: 'FU_ID' })
  facultyUser: FacultyUsers;

  @ManyToOne(() => Role, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'ROL_ID' })
  role: Role;
}
