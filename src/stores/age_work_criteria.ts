import { defineStore } from 'pinia';
import type { QTableProps } from 'quasar';
import { useQuasar } from 'quasar';
import { AgeWorkCriteriaService } from 'src/services/idp/ageWorkCriteriaService';
import type { AgeWorkCriteria, AgeWork } from 'src/types/idp';
import type { DataResponse } from 'src/types/data';
import { ref } from 'vue';

export const useAgeWorkCriteriaStore = defineStore('ageWorkCriteria', () => {
  const loading = ref(false);
  const $q = useQuasar();
  const service = new AgeWorkCriteriaService();

  const ageWorkCriteria = ref<AgeWorkCriteria[]>([]);
  const currentCriteria = ref<AgeWorkCriteria | null>(null);
  const ageWorks = ref<AgeWork[]>([]);
  const totalRecords = ref(0);
  const currentPage = ref(1);
  const hasNextPage = ref(false);
  const hasPrevPage = ref(false);

  async function fetchAgeWorkCriteria(pag: QTableProps['pagination'], search?: string) {
    loading.value = true;
    try {
      const res: DataResponse<AgeWorkCriteria> = await service.getAll(pag, search);
      ageWorkCriteria.value = res.data;
      totalRecords.value = res.total;
      currentPage.value = res.curPage;
      hasNextPage.value = res.hasNext;
      hasPrevPage.value = res.hasPrev;
      return res;
    } catch (err: unknown) {
      let message = 'โหลดข้อมูลเกณฑ์อายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchOneAgeWorkCriteria(id: number) {
    loading.value = true;
    try {
      const res = await service.fetchOne(id);
      currentCriteria.value = res;
      return res;
    } catch (err: unknown) {
      let message = 'โหลดข้อมูลเกณฑ์อายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function createAgeWorkCriteria(data: Omit<AgeWorkCriteria, 'id'>) {
    loading.value = true;
    try {
      const res = await service.create(data);
      ageWorkCriteria.value.push(res);
      $q.notify({ type: 'positive', message: 'สร้างเกณฑ์อายุงานสำเร็จ' });
      return res;
    } catch (err: unknown) {
      let message = 'สร้างเกณฑ์อายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function updateAgeWorkCriteria(id: number, data: Partial<AgeWorkCriteria>) {
    loading.value = true;
    try {
      const res = await service.update(id, data);
      const index = ageWorkCriteria.value.findIndex((item) => item.id === id);
      if (index !== -1) {
        ageWorkCriteria.value[index] = { ...ageWorkCriteria.value[index], ...res };
      }
      $q.notify({ type: 'positive', message: 'อัปเดตเกณฑ์อายุงานสำเร็จ' });
      return res;
    } catch (err: unknown) {
      let message = 'อัปเดตเกณฑ์อายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function removeAgeWorkCriteria(id: number) {
    loading.value = true;
    try {
      await service.remove(id);
      const index = ageWorkCriteria.value.findIndex((item) => item.id === id);
      if (index !== -1) {
        ageWorkCriteria.value.splice(index, 1);
      }
      $q.notify({ type: 'positive', message: 'ลบเกณฑ์อายุงานสำเร็จ' });
    } catch (err: unknown) {
      let message = 'ลบเกณฑ์อายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchAgeWorksByCriteria(
    id: number,
    pag: QTableProps['pagination'],
    search?: string,
  ) {
    loading.value = true;
    try {
      const res: DataResponse<AgeWork> = await service.getAgeWorksByCriteria(id, pag, search);
      ageWorks.value = res.data;
      totalRecords.value = res.total;
      currentPage.value = res.curPage;
      hasNextPage.value = res.hasNext;
      hasPrevPage.value = res.hasPrev;
      return res;
    } catch (err: unknown) {
      let message = 'โหลดข้อมูลอายุงานไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
      throw err;
    } finally {
      loading.value = false;
    }
  }

  return {
    // state
    ageWorkCriteria,
    currentCriteria,
    ageWorks,
    totalRecords,
    currentPage,
    hasNextPage,
    hasPrevPage,
    loading,

    // actions
    fetchAgeWorkCriteria,
    fetchOneAgeWorkCriteria,
    createAgeWorkCriteria,
    updateAgeWorkCriteria,
    removeAgeWorkCriteria,
    fetchAgeWorksByCriteria,
  };
});
