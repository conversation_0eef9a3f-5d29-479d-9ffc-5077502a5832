import { defineStore } from 'pinia';
import type { QTableProps } from 'quasar';
import { useQuasar } from 'quasar';
import { CompetenciesService } from 'src/services/competencies/competencies';
import type { Competency } from 'src/types/models';
import { ref } from 'vue';

export const useCompetencyStore = defineStore('competency', () => {
  const loading = ref(false);
  const $q = useQuasar();

  // Sample data for development
  const competencies: Competency[] = [
    {
      id: 1,
      name: 'การทำงานเป็นทีม',
      comp_type: 'สมรรถนะหลัก',
      career_type: 'วิชาการ',
      description: 'ความสามารถในการทำงานร่วมกับผู้อื่นอย่างมีประสิทธิภาพ',
    },
    {
      id: 2,
      name: 'ภาวะผู้นำ',
      comp_type: 'ทางการบริหาร',
      career_type: 'สนับสนุน',
      description: 'ความสามารถในการนำทีมและบริหารจัดการ',
    },
    {
      id: 3,
      name: 'การคิดวิเคราะห์',
      comp_type: 'ประจำสายงาน',
      career_type: 'วิชาการ',
      description: 'ความสามารถในการวิเคราะห์และแก้ปัญหาอย่างเป็นระบบ',
    },
  ];

  const competency = ref<Competency>({
    id: 0,
    name: '',
    comp_type: 'สมรรถนะหลัก',
    career_type: 'วิชาการ',
    description: '',
  });

  const editedCompetency = competency;

  async function fetchCompetencies(_pag: QTableProps['pagination']) {
    loading.value = true;
    try {
      const res = await new CompetenciesService().getAll(_pag);
      // competencies.value = res;
      console.log('fetchAll (response): ', res);
    } catch (err: unknown) {
      let message = 'โหลด Competencies ไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      loading.value = false;
    }
  }

  async function addCompetency(competency: Competency) {
    loading.value = true;
    try {
      const res = await new CompetenciesService().create(competency);
      console.log('create (response): ', res);
    } catch (err: unknown) {
      let message = 'สร้างสมรรถนะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      loading.value = false;
    }
  }

  function editCompetency(updatedCompetency: Competency) {
    const index = competencies.findIndex((item) => item.id === updatedCompetency.id);
    if (index !== -1) {
      competencies[index] = { ...updatedCompetency };
    }
  }

  return {
    // state
    competencies,
    editedCompetency,
    loading,

    // actions
    fetchCompetencies,
    addCompetency,
    editCompetency,
  };
});
