import { defineStore } from 'pinia';
import type { QTableProps } from 'quasar';
import { useQuasar } from 'quasar';
import { SkillsService } from 'src/services/skills/skills';
import type { SkillType } from 'src/types/data';
import type { Skill } from 'src/types/models';
import { ref } from 'vue';
//import apiService from '../services/apiService';

export const useSkillStore = defineStore('skill', () => {
  const $q = useQuasar();
  const createDialog = ref(false);
  const dialogTitile = ref('');
  const selectedTap = ref('');
  const isPreview = ref(false);
  const skills = ref<Skill[]>([]);
  const skill = ref<Skill>({
    id: 0,
    name: '',
    dep_id: 0,
    evaluatorId: 0,
    competencyIds: [0],
    career_type: '',
    programId: 0,
    tracking: false,
  });
  const editedSkill = skill;

  async function fetchSkills(type: SkillType, _pag: QTableProps['pagination']) {
    try {
      const res = await new SkillsService(type).getAll(_pag, type);
      skills.value = res.data;
    } catch (err: unknown) {
      let message = 'โหลด Skills ไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    }
  }

  async function fetchOne(id: number, type: string) {
    try {
      const res = await new SkillsService(type).fecthOne(id);
      editedSkill.value = res;
      //editedSkill competecies[] compete
      editedSkill.value.competencyIds =
        editedSkill.value.competencies
          ?.map((c) => c?.id)
          .filter((id): id is number => typeof id === 'number') ?? [];
    } catch (err: unknown) {
      let message = 'โหลด Skills ไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    }
  }

  async function addSkill(skill: Skill) {
    try {
      await new SkillsService(skill.career_type).create(skill);
    } catch (err: unknown) {
      let message = 'สร้างทักษะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      createDialog.value = false;
      $q.notify({
        message: 'สร้างทักษะใหม่สำเร็จ!',
        color: 'positive',
        icon: 'check_circle',
      });
    }
  }

  async function editSkill(updatedSkill: Skill) {
    try {
      if (updatedSkill.id) {
        await new SkillsService(updatedSkill.career_type).update(updatedSkill.id, updatedSkill);
      }
    } catch (err: unknown) {
      let message = 'สร้างทักษะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      createDialog.value = false;
      $q.notify({
        message: 'แก้ไขทักษะสำเร็จ!',
        color: 'positive',
        icon: 'check_circle',
      });
    }
  }

  function cleanValue() {
    editedSkill.value = {
      id: 0,
      name: '',
      dep_id: 0,
      evaluatorId: 0,
      competencyIds: [0],
      career_type: '',
      programId: 0,
      tracking: false,
    };
  }

  async function deleteSkill(skillId: number, type: string) {
    try {
      await new SkillsService(type).remove(skillId);
      cleanValue();
    } catch (err: unknown) {
      let message = 'ลบทักษะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    }
  }
  return {
    //state
    createDialog,
    skills,
    editedSkill,
    dialogTitile,
    selectedTap,
    isPreview,

    //action
    fetchSkills,
    fetchOne,
    addSkill,
    editSkill,
    cleanValue,
    deleteSkill,
  };
});
