import { Module } from '@nestjs/common';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from '../roles/entities/role.entity';
import { User } from './entities/user.entity';
import { UsersService } from './users.service';
import { Faculty } from 'src/faculties/entities/faculty.entity';
import { FacultyUsers } from 'src/faculties/entities/faculty-user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, Role, Faculty, FacultyUsers])],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService, UsersModule],
})
export class UsersModule {}
