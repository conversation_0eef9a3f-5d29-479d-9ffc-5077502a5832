import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { UsersService, UserListItem } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import {
  UpdateUserDto,
} from './dto/update-user.dto';
import { UpdateUserFacultiesDto } from '../../faculties/dto/update-faculty-user.dto';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import type { DataParams } from 'src/types/params';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import { RequirePermissions } from 'src/common/decorators/permissions.decorator';

@ApiBearerAuth()
@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @RequirePermissions('user_create')
  @ApiOperation({ summary: 'Create a new user' })
  @ApiBody({
    type: CreateUserDto,
    examples: {
      user: {
        value: {
          name: 'John Doe',
          email: '<EMAIL>',
          password: 'Password123',
          roleIds: [1, 2],
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'User successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @RequirePermissions('user_read')
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Return all users' })
  async findAll(
    @Query() query: DataParams,
  ): Promise<{
    data: UserListItem[];
    total: number;
    curPage: number;
    hasPrev: boolean;
    hasNext: boolean;
  }> {
    return this.usersService.findAll(query);
  }

  @Get(':id')
  @RequirePermissions('user_read')
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'Return the user' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string) {
    return this.usersService.findOne(+id);
  }

  @Patch(':id')
  @RequirePermissions('user_update')
  @ApiOperation({ summary: 'Update user information, password, or roles' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Updated Name' },
        email: { type: 'string', example: '<EMAIL>' },
        currentPassword: { type: 'string', example: 'CurrentPassword123' },
        newPassword: { type: 'string', example: 'NewPassword123' },
        roleIds: { type: 'array', items: { type: 'number' }, example: [1, 2] },
      },
      required: [],
    },
    examples: {
      updateInfo: {
        value: {
          name: 'Updated Name',
          email: '<EMAIL>',
        },
      },
      updatePassword: {
        value: {
          currentPassword: 'CurrentPassword123',
          newPassword: 'NewPassword123',
        },
      },
      updateRoles: {
        value: {
          roleIds: [1, 2],
        },
      },
      updateAll: {
        value: {
          name: 'Updated Name',
          email: '<EMAIL>',
          currentPassword: 'CurrentPassword123',
          newPassword: 'NewPassword123',
          roleIds: [1, 2],
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'User successfully updated' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async update(
    @Param('id') id: string,
    @Body()
    updateUserDto: UpdateUserDto & {
      currentPassword?: string;
      newPassword?: string;
      roleIds?: number[];
    },
  ) {
    return this.usersService.update(+id, updateUserDto);
  }

  // @Patch(':id/password')
  // @HttpCode(HttpStatus.NO_CONTENT)
  // @ApiOperation({ summary: 'Update user password' })
  // @ApiParam({ name: 'id', description: 'User ID' })
  // @ApiBody({
  //   type: UpdateUserPasswordDto,
  //   examples: {
  //     passwordUpdate: {
  //       value: {
  //         currentPassword: 'CurrentPassword123',
  //         newPassword: 'NewPassword123',
  //       },
  //     },
  //   },
  // })
  // @ApiResponse({ status: 204, description: 'Password successfully updated' })
  // @ApiResponse({ status: 400, description: 'Invalid password' })
  // @ApiResponse({ status: 404, description: 'User not found' })
  // async updatePassword(
  //   @Param('id') id: string,
  //   @Body() updateUserPasswordDto: UpdateUserPasswordDto,
  // ) {
  //   await this.usersService.updateUserPassword(+id, updateUserPasswordDto);
  // }

  // @Patch(':id/roles')
  // @ApiOperation({ summary: 'Update user roles' })
  // @ApiParam({ name: 'id', description: 'User ID' })
  // @ApiBody({
  //   type: UpdateUserRolesDto,
  //   examples: {
  //     rolesUpdate: {
  //       value: {
  //         roleIds: [1, 3, 5],
  //       },
  //     },
  //   },
  // })
  // @ApiResponse({ status: 200, description: 'Roles successfully updated' })
  // @ApiResponse({ status: 404, description: 'User not found' })
  // async updateRoles(
  //   @Param('id') id: string,
  //   @Body() updateUserRolesDto: UpdateUserRolesDto,
  // ) {
  //   return this.usersService.updateUserRoles(+id, updateUserRolesDto);
  // }

  @Delete(':id')
  @RequirePermissions('user_delete')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a user' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 204, description: 'User successfully deleted' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async remove(@Param('id') id: string) {
    await this.usersService.remove(+id);
  }

  // @Patch(':id/password')
  // @HttpCode(HttpStatus.NO_CONTENT)
  // @ApiOperation({ summary: 'Update user password' })
  // @ApiParam({ name: 'id', description: 'User ID' })
  // @ApiBody({
  //   type: UpdateUserPasswordDto,
  //   examples: {
  //     passwordUpdate: {
  //       value: {
  //         currentPassword: 'CurrentPassword123',
  //         newPassword: 'NewPassword123',
  //       },
  //     },
  //   },
  // })
  // @ApiResponse({ status: 204, description: 'Password successfully updated' })
  // @ApiResponse({ status: 400, description: 'Invalid password' })
  // @ApiResponse({ status: 404, description: 'User not found' })
  // async updateUserPassword(
  //   @Param('id') id: string,
  //   @Body() updateUserPasswordDto: UpdateUserPasswordDto,
  // ): Promise<void> {
  //   await this.usersService.updateUserPassword(+id, updateUserPasswordDto);
  // }

  @Get(':id/faculties')
  @RequirePermissions('user_read')
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all faculties with user status for dialog' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'Return all faculties with user status' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getFacultiesWithUserStatus(
    @Param('id') id: string,
    @Query() query: DataParams,
  ) {
    return this.usersService.getFacultiesWithUserStatus(+id, query);
  }

  @Patch(':id/faculties')
  @RequirePermissions('user_update')
  @ApiOperation({ summary: 'Update user faculties based on checkbox selection' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({
    type: UpdateUserFacultiesDto,
    examples: {
      updateFaculties: {
        value: {
          facultyIds: [1, 2, 3],
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'User faculties successfully updated' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUserFaculties(
    @Param('id') id: string,
    @Body() updateUserFacultiesDto: UpdateUserFacultiesDto,
  ) {
    return this.usersService.updateUserFaculties(+id, updateUserFacultiesDto.facultyIds);
  }
}
