import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'HRD_UMS_PERMISSIONS' })
export class UmsPermission {
  @PrimaryGeneratedColumn({ name: 'PER_ID', comment: 'รหัสสิทธิ์' })
  perId: number;

  @Column({
    name: 'PER_NAME_TH',
    comment: 'ชื่อสิทธิ์ภาษาไทย',
    length: 50,
    nullable: false,
  })
  perNameTh: string;

  @Column({ name: 'PER_NAME_EN', comment: 'ชื่อสิทธิ์ภาษาอังกฤษ', length: 50 })
  perNameEn: string;

  @Column({
    name: 'PER_STATUS',
    comment: 'สถานะ Y = เปิด N = ปิด',
    length: 1,
    default: 'Y',
  })
  perStatus: string;

  @Column({
    name: 'PER_DEFAULT',
    comment: 'สิทธ์ตั้งต้น Y = เปิด N = ปิด',
    length: 1,
    default: 'Y',
  })
  perDefault: string;

  @CreateDateColumn({
    name: 'PER_CREATE_AT',
    comment: 'วันที่สร้าง',
    nullable: true,
  })
  createAt: Date;

  @DeleteDateColumn({
    name: 'PER_DELETE_AT',
    comment: 'วันที่ลบ',
    nullable: true,
  })
  deleteAt: Date;

  @UpdateDateColumn({
    name: 'PER_UPDATE_AT',
    comment: 'วันที่อัพเดท',
    nullable: true,
  })
  updateAt: Date;

  @Column({
    name: 'PER_UPDATE_USER',
    comment: 'คนที่อัพเดท',
    length: 100,
    nullable: true,
  })
  updateUser: string;

  @Column({
    name: 'PER_UPDATE_PROGRAM',
    comment: 'โปรแกรมที่อัพเดท',
    length: 100,
    nullable: true,
  })
  updateProgram: string;
}
