import { FacultyUserRoles } from 'src/faculties/entities/faculty-user-role.entity';
import { Permission } from 'src/resources/permissions/entities/permission.entity';
import { User } from 'src/resources/users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  JoinTable,
  OneToMany,
  JoinC<PERSON>um<PERSON>,
} from 'typeorm';

@Entity('HRD_ROLES')
export class Role {
  @PrimaryGeneratedColumn({ name: 'ROL_ID' })
  id: number;

  @Column({ name: 'ROL_NAME', unique: true, nullable: false })
  name: string;

  @Column({ name: 'ROL_DESCRIPTION', nullable: true })
  description: string;

  @Column({ name: 'ROL_DEPARTMENT', nullable: true })
  department: string;

  @ManyToMany(() => Permission, (permission) => permission.roles)
  @JoinTable({
    name: 'HRD_ROLES_HAS_PERMISSIONS',
    joinColumn: { name: 'ROL_<PERSON>', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'PER_ID', referencedColumnName: 'id' },
  })
  permissions: Permission[];

  @OneToMany(() => FacultyUserRoles, (facultyRoles) => facultyRoles.role, {
    cascade: true,
  })
  facultyRoles: FacultyUserRoles[];
}
