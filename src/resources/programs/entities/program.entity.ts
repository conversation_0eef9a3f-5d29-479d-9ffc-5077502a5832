import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Assessment } from '../../assessment-forms/entities/assessment.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('IDP_PROGRAMS')
export class Program {
  @PrimaryGeneratedColumn({ name: 'PRG_ID' })
  id: number;

  @Column({ name: 'PRG_NAME', type: 'text' })
  name: string;

  @Column({ name: 'PRG_DESCRIPTION', type: 'text' })
  description: string;

  @ManyToOne(() => User, (user) => user.programs)
  @JoinColumn({ name: 'PRG_CREATOR_ID' })
  creator: User;

  @OneToMany(() => Assessment, (assessment) => assessment.program)
  assessments: Assessment[];
}
