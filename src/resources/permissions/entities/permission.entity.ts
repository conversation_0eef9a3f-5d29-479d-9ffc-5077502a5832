import { Role } from 'src/resources/roles/entities/role.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
} from 'typeorm';

@Entity('HRD_PERMISSIONS')
export class Permission {
  @PrimaryGeneratedColumn({ name: 'PER_ID' })
  id: number;

  @Column({ name: 'PER_NAME', nullable: false })
  name: string;

  @Column({ name: 'PER_DESC_EN', nullable: false })
  descEn: string;

  @Column({ name: 'PER_DESC_TH', nullable: false })
  descTh: string;

  @Column({ name: 'PER_STATUS', default: true })
  status: boolean;

  @Column({ name: 'PER_IS_DEFAULT', default: false })
  isDefault: boolean;

  @ManyToMany(() => Role, (role) => role.permissions)
  roles: Role[];
}
