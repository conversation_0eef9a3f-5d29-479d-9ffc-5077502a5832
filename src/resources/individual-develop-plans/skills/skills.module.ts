import { Modu<PERSON> } from '@nestjs/common';
import { SkillsController } from './skills.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Skill } from './entities/skill.entity';
import { SkillsService } from './skills.service';
import { User } from '../../users/entities/user.entity';
import { Program } from '../../programs/entities/program.entity';
import { PermAuditSkill } from './entities/perm-audit-skill.entity';
import { UserSkill } from './entities/user-skill.entity';
import { CompetencySkill } from './entities/competency-skill.entity';
import { Competency } from '../competencies/entities/competencies.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Skill, User, Program, PermAuditSkill, UserSkill, CompetencySkill, Competency]),
  ],
  controllers: [SkillsController],
  providers: [SkillsService],
  exports: [SkillsService, SkillsModule],
})
export class SkillsModule {}
