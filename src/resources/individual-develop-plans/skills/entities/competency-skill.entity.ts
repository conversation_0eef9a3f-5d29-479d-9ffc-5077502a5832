import { Competency } from 'src/resources/individual-develop-plans/competencies/entities/competencies.entity';
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  Column,
} from 'typeorm';
import { Skill } from './skill.entity';

// competency-skill.entity.ts
@Entity('IDP_COMPETENCY_SKILLS')
export class CompetencySkill {
  @PrimaryGeneratedColumn({ name: 'CPS_ID' })
  id: number;

  @ManyToOne(() => Competency, (c) => c.competencySkills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'CPT_ID' })
  competency: Competency;

  @ManyToOne(() => Skill, (s) => s.competencySkills, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'SK_ID' })
  skill: Skill;
}
