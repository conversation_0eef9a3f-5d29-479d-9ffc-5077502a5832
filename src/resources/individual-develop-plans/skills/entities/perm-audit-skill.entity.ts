import { User } from 'src/resources/users/entities/user.entity';
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Column,
} from 'typeorm';
import { Skill } from './skill.entity';

@Entity('IDP_PERM_AUDIT_SKILLS')
export class PermAuditSkill {
  @PrimaryGeneratedColumn({ name: 'PAS_ID' })
  id: number;

  @ManyToOne(() => User, (user) => user.permAuditSkills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'PAS_USER_ID' })
  user: User;

  @ManyToOne(() => Skill, (skill) => skill.permAuditSkills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'PAS_SKILL_ID' })
  skill: Skill;

  @CreateDateColumn({ name: 'PAS_CREATED_AT' })
  createdAt: Date;
}
