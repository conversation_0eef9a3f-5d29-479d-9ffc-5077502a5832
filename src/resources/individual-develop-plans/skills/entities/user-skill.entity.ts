import { User } from "src/resources/users/entities/user.entity";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { Skill } from "./skill.entity";

@Entity('IDP_USER_SKILLS')
export class UserSkill {
  @PrimaryGeneratedColumn({ name: 'USK_ID' })
  id: number;

  @ManyToOne(() => User, (user) => user.userSkills, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'USK_USER_ID' })
  user: User;

  @ManyToOne(() => Skill, (skill) => skill.userSkills, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'USK_SKILL_ID' })
  skill: Skill;

  @Column({ name: 'USK_IS_EXTRA', default: false })
  is_extra: boolean;

  @Column({
    name: 'USK_STATUS',
    type: 'enum',
    enum: ['รออนุมัติ', 'อนุมัติ', 'ไม่อนุมัติ', 'รอแก้ไข', 'พักตอบ'],
    default: 'รออนุมัติ',
  })
  status: 'รออนุมัติ' | 'อนุมัติ' | 'ไม่อนุมัติ' | 'รอแก้ไข' | 'พักตอบ';

  @Column({ name: 'USK_APPROVED_AT', nullable: true })
  approved_at: Date;
}
