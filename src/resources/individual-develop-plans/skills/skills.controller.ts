import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { SkillsService } from './skills.service';
import { CreateSkillDto } from './dto/create-skill.dto';
import { UpdateSkillDto } from './dto/update-skill.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/auth/auth.guard';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import type { DataParams } from 'src/types/params';
import { PlanType } from '../type-plans/entities/type-plan.entity';

@ApiBearerAuth()
// @UseGuards(AuthGuard)
@ApiTags('Skills')
@Controller('skills')
export class SkillsController {
  constructor(private readonly skillsService: SkillsService) {}

  @Post()
  async create(@Body() dto: CreateSkillDto) {
    console.log(dto);
    return this.skillsService.create(dto);
  }

  @Get()
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all skills (with pagination)' })
  @ApiResponse({ status: 200 })
  @ApiQuery({
    enum: PlanType,
    required: false,
  })
  findAll(@Query() query: DataParams & { career_type?: PlanType }) {
    return this.skillsService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get skill by ID' })
  @ApiParam({ name: 'id', description: 'Skill ID' })
  @ApiResponse({ status: 200 })
  @ApiResponse({ status: 404, description: 'Skill not found' })
  async findOne(@Param('id') id: string) {
    return this.skillsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a skill' })
  @ApiParam({ name: 'id', description: 'Skill ID' })
  @ApiBody({ type: UpdateSkillDto })
  @ApiResponse({ status: 200 })
  @ApiResponse({ status: 404, description: 'Skill not found' })
  async update(@Param('id') id: string, @Body() dto: UpdateSkillDto) {
    return this.skillsService.update(+id, dto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a skill' })
  @ApiParam({ name: 'id', description: 'Skill ID' })
  @ApiResponse({ status: 204, description: 'Deleted successfully' })
  async remove(@Param('id') id: string) {
    await this.skillsService.remove(+id);
  }
}
