import { IsBoolean, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateDevelopmentPlanDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = false;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  facId?: number;

  @ApiProperty()
  @IsNumber()
  ageWorkCriteriaId: number;

  // @ApiProperty()
  // @IsNumber()
  // creatorId: number;
}
