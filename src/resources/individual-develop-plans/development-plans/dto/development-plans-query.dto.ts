import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';

export class DevelopmentPlansQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({ description: 'Search term for name or description' })
  @IsOptional()
  search?: string;

  // is centrall will sent 'central' else sent 'department'
  @ApiProperty({
    description: 'Filter by isCentral',
    required: false,
  })
  @IsString()
  isCentral?: 'central' | 'department';

  @ApiPropertyOptional({
    description: 'Filter by parent ID',
    required: false,
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  parentId?: number;
}
