import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseBoolPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DevelopmentPlansService } from './development-plans.service';
import { CreateDevelopmentPlanDto } from './dto/create-development-plan.dto';
import { UpdateDevelopmentPlanDto } from './dto/update-development-plan.dto';
import { DevelopmentPlansQueryDto } from './dto/development-plans-query.dto';
import { DevelopmentPlanResponseDto } from './dto/development-plans-response.dto';

@ApiTags('Development Plans')
@ApiBearerAuth()
@Controller('development-plans')
export class DevelopmentPlansController {
  constructor(
    private readonly developmentPlansService: DevelopmentPlansService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new development plan' })
  @ApiResponse({
    status: 201,
    description: 'The development plan has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  create(@Body() createDevelopmentPlanDto: CreateDevelopmentPlanDto) {
    return this.developmentPlansService.create(createDevelopmentPlanDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all development plans with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of development plans',
    type: DevelopmentPlanResponseDto,
  })
  async findAll(
    @Query() query: DevelopmentPlansQueryDto,
  ): Promise<DevelopmentPlanResponseDto> {
    console.log('Raw query params:', query);
    const [data, total] = await this.developmentPlansService.findAll(query);
    return new DevelopmentPlanResponseDto(data, total, query.page, query.limit);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get development plan by ID' })
  @ApiResponse({ status: 200, description: 'Returns the development plan' })
  @ApiResponse({ status: 404, description: 'Development plan not found' })
  findOne(
    @Param('id') id: string,
    @Query('isCentral', ParseBoolPipe) isCentral: boolean,
  ) {
    console.log(isCentral);
    return this.developmentPlansService.findOne(+id, isCentral);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a development plan' })
  @ApiResponse({
    status: 200,
    description: 'The development plan has been successfully updated.',
  })
  @ApiResponse({ status: 404, description: 'Development plan not found' })
  update(
    @Param('id') id: string,
    @Body() updateDevelopmentPlanDto: UpdateDevelopmentPlanDto,
  ) {
    return this.developmentPlansService.update(+id, updateDevelopmentPlanDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a development plan' })
  @ApiResponse({
    status: 200,
    description: 'The development plan has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Development plan not found' })
  remove(@Param('id') id: string) {
    return this.developmentPlansService.remove(+id);
  }
}
