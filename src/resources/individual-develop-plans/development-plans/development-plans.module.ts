import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DevelopmentPlansService } from './development-plans.service';
import { DevelopmentPlansController } from './development-plans.controller';
import { DevelopmentPlan } from './entities/development-plan.entity';
import { AgeWorkCriteria } from '../age-work/entities/age-work-criteria.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DevelopmentPlan, AgeWorkCriteria])],
  controllers: [DevelopmentPlansController],
  providers: [DevelopmentPlansService],
  exports: [DevelopmentPlansService],
})
export class DevelopmentPlansModule {}
