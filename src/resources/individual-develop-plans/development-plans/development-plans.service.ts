import { Injectable } from '@nestjs/common';
import { CreateDevelopmentPlanDto } from './dto/create-development-plan.dto';
import { UpdateDevelopmentPlanDto } from './dto/update-development-plan.dto';
import { DevelopmentPlansQueryDto } from './dto/development-plans-query.dto';
import type { DevelopmentPlanParams } from 'src/types/params';
import {
  type EntityManager,
  Like,
  FindManyOptions,
  FindOptionsWhere,
  FindOptionsOrder,
  IsNull,
  Not,
} from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import { DevelopmentPlan } from './entities/development-plan.entity';
import { AgeWork } from '../age-work/entities/age-work.entity';
import { Position } from 'src/positions/entities/position.entity';
import { Career } from '../careers/entities/careers.entity';
import { PlanType, TypePlan } from '../type-plans/entities/type-plan.entity';
import { AgeWorkCriteria } from '../age-work/entities/age-work-criteria.entity';
import { Faculty } from 'src/faculties/entities/faculty.entity';

@Injectable()
export class DevelopmentPlansService {
  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
  ) {}

  async create(createDevelopmentPlanDto: CreateDevelopmentPlanDto) {
    // 1. Create the development plan
    const ent = this.entityManager.create(
      DevelopmentPlan,
      createDevelopmentPlanDto,
    );
    const devplan = await this.entityManager.save(ent);

    // 2. Get all required repositories
    const repoAgeWorkCriteria =
      this.entityManager.getRepository(AgeWorkCriteria);
    const repoPosition = this.entityManager.getRepository(Position);
    const repoTypePlan = this.entityManager.getRepository(TypePlan);

    // 3. Fetch all age works and positions
    const ageWorkCriteria = await repoAgeWorkCriteria.findOne({
      where: { id: createDevelopmentPlanDto.ageWorkCriteriaId },
      relations: { ageWorks: true },
      select: { id: true, ageWorks: { id: true } },
    });
    const positions = await repoPosition.find({ select: { id: true } });

    // 4. Generate type plans for each PlanType
    const planTypes = Object.values(PlanType);
    const typePlanPromises: Promise<TypePlan>[] = [];

    for (const planType of planTypes) {
      // Skip career type as it's optional and user-defined
      if (planType === PlanType.POSITION) continue;

      // Create type plans for each age work
      if (planType === PlanType.SPECIFIC_MANAGEMENT) {
        for (const position of positions) {
          const typePlan = repoTypePlan.create({
            name: planType,
            developmentPlanId: devplan.id,
            ageWorkId: null,
            positionId: position.id,
            careerId: null,
          });
          typePlanPromises.push(repoTypePlan.save(typePlan));
        }
      } else if (planType === PlanType.GENERAL_MANAGER) {
        const typePlan = repoTypePlan.create({
          name: planType,
          developmentPlanId: devplan.id,
          ageWorkId: null,
          positionId: null,
          careerId: null,
        });
        typePlanPromises.push(repoTypePlan.save(typePlan));
      } else {
        for (const ageWork of ageWorkCriteria.ageWorks) {
          const typePlan = repoTypePlan.create({
            name: planType,
            developmentPlanId: devplan.id,
            ageWorkId: ageWork.id,
            positionId: null,
            careerId: null,
          });
          typePlanPromises.push(repoTypePlan.save(typePlan));
        }
      }
    }

    // 5. Wait for all type plans to be created
    devplan.typePlans = await Promise.all(typePlanPromises);

    // 6. Create department-specific plans
    const repoFac = this.entityManager.getRepository(Faculty);
    const faculties = await repoFac.find({ select: { id: true } });
    for (const faculty of faculties) {
      const newDevPlan = this.entityManager.create(DevelopmentPlan, {
        ...createDevelopmentPlanDto,
        facultyId: faculty.id,
        parentId: devplan.id,
      });
      await this.entityManager.save(newDevPlan);
    }

    return devplan;
  }

  async findAll(
    query: DevelopmentPlansQueryDto,
  ): Promise<[DevelopmentPlan[], number]> {
    const repoDev = this.entityManager.getRepository(DevelopmentPlan);

    const whereConditions: FindOptionsWhere<DevelopmentPlan> = {};
    const orConditions: FindOptionsWhere<DevelopmentPlan>[] = [];

    // Handle search
    if (query.search) {
      orConditions.push(
        { name: Like(`%${query.search}%`) },
        { description: Like(`%${query.search}%`) },
      );
    }

    // Handle isCentral filter (root nodes)
    if (query.isCentral === 'central') {
      whereConditions.parentId = IsNull();
    } else if (query.isCentral === 'department') {
      // get only leaf node
      whereConditions.parentId = Not(IsNull());
    }

    // Handle parentId filter
    if (query.parentId) {
      whereConditions.parentId = query.parentId;
    }

    // Combine conditions
    let where:
      | FindOptionsWhere<DevelopmentPlan>
      | FindOptionsWhere<DevelopmentPlan>[] = whereConditions;

    // If there are OR conditions, combine them with AND with the whereConditions
    if (orConditions.length > 0) {
      where = [
        ...orConditions.map((cond) => ({
          ...cond,
          ...(Object.keys(whereConditions).length > 0 ? whereConditions : {}),
        })),
      ];
    }

    const findOptions: FindManyOptions<DevelopmentPlan> = {
      where: where,
      order: {
        [query.sortBy]: query.order,
      } as FindOptionsOrder<DevelopmentPlan>,
      take: query.limit,
      skip: (query.page - 1) * query.limit,
    };

    return repoDev.findAndCount(findOptions);
  }

  async findOne(devId: number, isCentral: boolean): Promise<DevelopmentPlan> {
    console.log(isCentral);
    const repoDev = this.entityManager.getRepository(DevelopmentPlan);

    const devPlan = await repoDev.findOne({
      where: { id: devId },
      relations: {
        typePlans: {
          ageWork: true,
          position: true,
          career: true,
          skills: true,
        },
      },
    });

    let children: DevelopmentPlan[] = [];
    console.log('ture isCentral', isCentral);
    if (!isCentral) {
      children = await repoDev.find({
        where: { parentId: devId },
        relations: {
          typePlans: {
            ageWork: true,
            position: true,
            career: true,
            skills: true,
            privatePlanUser: true,
          },
        },
      });
      console.log('!isCentral', isCentral);
      console.log(children);
    }
    devPlan.children = children;
    return devPlan;
  }

  update(id: number, updateDevelopmentPlanDto: UpdateDevelopmentPlanDto) {
    return this.entityManager.update(
      DevelopmentPlan,
      { id },
      updateDevelopmentPlanDto,
    );
  }

  remove(id: number) {
    return this.entityManager.delete(DevelopmentPlan, { id });
  }
}
