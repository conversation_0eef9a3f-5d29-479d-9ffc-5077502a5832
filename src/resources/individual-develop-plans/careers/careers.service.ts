import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Career } from './entities/careers.entity';
import { Repository } from 'typeorm';

@Injectable()
export class CareersService {
  constructor(
    @InjectRepository(Career)
    private readonly careerRepo: Repository<Career>,
  ) {}
  // Example method to get all careers
  findAll(): string[] {
    return ['Career ', 'Career 2', 'Career 3'];
  }

  // Example method to get a career by id
  findOne(id: number): string {
    return `Career ${id}`;
  }

  // Example method to create a career
  create(career: string): string {
    return `Created career: ${career}`;
  }

  // Example method to update a career
  update(id: number, career: string): string {
    return `Updated career ${id} to ${career}`;
  }

  // Example method to delete a career
  remove(id: number): string {
    return `Removed career ${id}`;
  }
}
