import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';

@Controller('careers')
export class CareersController {
    @Get()
    findAll() {
        // Return all careers
        return [];
    }

    @Get(':id')
    findOne(@Param('id') id: string) {
        // Return a single career by id
        return { id };
    }

    @Post()
    create(@Body() createCareerDto: any) {
        // Create a new career
        return createCareerDto;
    }

    @Put(':id')
    update(@Param('id') id: string, @Body() updateCareerDto: any) {
        // Update a career by id
        return { id, ...updateCareerDto };
    }

    @Delete(':id')
    remove(@Param('id') id: string) {
        // Remove a career by id
        return { deleted: id };
    }
}
