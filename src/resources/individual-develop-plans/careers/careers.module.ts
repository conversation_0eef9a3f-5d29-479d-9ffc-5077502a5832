import { Modu<PERSON> } from '@nestjs/common';
import { CareersController } from './careers.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Career } from './entities/careers.entity';
import { CareersService } from './careers.service';
import { Role } from 'src/resources/roles/entities/role.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Career,
      Role, // Ensure Role entity is imported for relations
    ]),
  ],
  controllers: [CareersController],
  providers: [CareersService],
  exports: [CareersService],
})
export class CareersModule {}
