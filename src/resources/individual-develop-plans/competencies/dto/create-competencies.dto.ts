import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsEnum, IsArray, IsInt } from 'class-validator';
import { CompetencyType } from '../enum/competencies-type.enum';

export class CreateCompetenciesDto {
  @ApiProperty({ example: 'สมรรถนะหลัก' })
  @IsOptional()
  @IsEnum(CompetencyType)
  career_type?: CompetencyType;

  @ApiProperty({ example: 'การคิดเชิงวิเคราะห์' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'สามารถวิเคราะห์ปัญหาได้อย่างเป็นระบบ' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false, example: [1, 2] })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  skillIds?: number[];
}
