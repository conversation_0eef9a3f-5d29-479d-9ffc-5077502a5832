import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompetenciesService } from './competencies.service';
import { CompetenciesController } from './competencies.controller';
import { Competency } from './entities/competencies.entity';
import { CompetencySkill } from 'src/resources/individual-develop-plans/skills/entities/competency-skill.entity';
import { Skill } from 'src/resources/individual-develop-plans/skills/entities/skill.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Competency,
      CompetencySkill,
      Skill,
    ]),
  ],
  controllers: [CompetenciesController],
  providers: [CompetenciesService],
  exports: [CompetenciesService, TypeOrmModule],
})
export class CompetenciesModule {}
