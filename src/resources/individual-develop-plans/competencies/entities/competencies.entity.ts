import { CompetencySkill } from 'src/resources/individual-develop-plans/skills/entities/competency-skill.entity';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { CompetencyType } from '../enum/competencies-type.enum';

@Entity('IDP_COMPETENCIES')
export class Competency {
  @PrimaryGeneratedColumn({ name: 'CPT_ID' })
  id: number;

  @Column({
    name: 'CPT_CAREER_TYPE',
    type: 'enum',
    enum: CompetencyType,
  })
  career_type: CompetencyType;

  @Column({ name: 'CPT_NAME' })
  name: string;

  @Column({ name: 'CPT_DESCRIPTION', nullable: true })
  description: string;

  @OneToMany(() => CompetencySkill, (cs) => cs.competency)
  competencySkills: CompetencySkill[];
}
