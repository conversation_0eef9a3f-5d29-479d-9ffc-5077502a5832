import { Controller, Get, Post, Put, Delete, Param, Body } from '@nestjs/common';
import { CareersRecordsService } from './career-records.service';

@Controller('career-records')
export class CareersRecordsController {
  constructor(private readonly careersRecordsService: CareersRecordsService) {}

  @Get()
  findAll() {
    return this.careersRecordsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.careersRecordsService.findOne(Number(id));
  }

  @Post()
  create(@Body() createRecordDto: any) {
    return this.careersRecordsService.create(createRecordDto);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateRecordDto: any) {
    return this.careersRecordsService.update(Number(id), updateRecordDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.careersRecordsService.remove(Number(id));
  }
}