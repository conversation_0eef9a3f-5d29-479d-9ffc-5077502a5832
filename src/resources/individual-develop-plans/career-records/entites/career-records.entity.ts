import { User } from "src/resources/users/entities/user.entity";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { Career } from "../../careers/entities/careers.entity";

@Entity('IDP_CAREER_RECORDS')
export class CareerRecords {
 
  
  @PrimaryGeneratedColumn({ name: 'CRR_ID' })
  id: number;

  @JoinColumn(
    { name: 'CRR_CAREER_ID' }
  )
  @ManyToOne(() => Career, (career) => career.careerRecords)
  career_Id: number;

  @JoinColumn({ name: 'CRR_USER_ID' })
  @ManyToOne(() => User, (user) => user.careerRecords)
  user: User;

  @Column({ name: 'CRR_START_DATE' })
  start_date: Date;

  @Column({ name: 'CRR_END_DATE', nullable: true })
  end_date: Date;

 
}