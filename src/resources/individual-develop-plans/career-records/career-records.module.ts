import { Modu<PERSON> } from '@nestjs/common';
import { CareersRecordsController } from './career-records.controller';
import { CareersRecordsService } from './career-records.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CareerRecords } from './entites/career-records.entity';
import { User } from 'src/resources/users/entities/user.entity';
import { Career } from '../careers/entities/careers.entity';

@Module({
   imports: [
      TypeOrmModule.forFeature([
        CareerRecords,
        User, // Ensure User entity is imported for relations
        Career, // Ensure Career entity is imported for relations
      ]),
    ],
  controllers: [CareersRecordsController],
  providers: [CareersRecordsService],
  exports: [CareersRecordsService],
})
export class CareersRecordsModule {}