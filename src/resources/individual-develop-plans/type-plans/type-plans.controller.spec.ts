import { Test, TestingModule } from '@nestjs/testing';
import { TypePlansController } from './type-plans.controller';
import { TypePlansService } from './type-plans.service';

describe('TypePlansController', () => {
  let controller: TypePlansController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TypePlansController],
      providers: [TypePlansService],
    }).compile();

    controller = module.get<TypePlansController>(TypePlansController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
