import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateTypePlanDto } from './dto/create-type-plan.dto';
import { UpdateTypePlanDto } from './dto/update-type-plan.dto';
import { EntityManager } from 'typeorm';
import { PlanType, TypePlan } from './entities/type-plan.entity';
import { RemoveSkillDto, SelectSkillsDto } from './dto/utils.dto';
import { Skill } from '../skills/entities/skill.entity';
import { AgeWork } from '../age-work/entities/age-work.entity';
import { AgeWorkCriteria } from '../age-work/entities/age-work-criteria.entity';
import { DevelopmentPlan } from '../development-plans/entities/development-plan.entity';

@Injectable()
export class TypePlansService {
  constructor(private readonly entityManager: EntityManager) {}

  async create(createTypePlanDto: CreateTypePlanDto) {
    const { developmentPlanId, name, careerId, privateUserId } =
      createTypePlanDto;

    const repoDevelopmentPlan =
      this.entityManager.getRepository(DevelopmentPlan);
    const developmentPlan = await repoDevelopmentPlan.findOneOrFail({
      where: { id: developmentPlanId },
      relations: { ageWorkCriteria: { ageWorks: true } },
      select: {
        id: true,
        ageWorkCriteria: { id: true, ageWorks: { id: true } },
      },
    });

    const typePlans: TypePlan[] = [];

    try {
      for (const ageWork of developmentPlan.ageWorkCriteria.ageWorks) {
        const typePlan = this.entityManager.create(TypePlan, {
          developmentPlanId: developmentPlanId,
          name: name,
          ageWorkId: ageWork.id,
          careerId: careerId,
          privateUserId: privateUserId,
        });
        typePlans.push(typePlan);
      }
    } catch (error) {
      console.error('Error creating type plans:', error);
      throw error;
    }
    return this.entityManager.save(typePlans);
  }

  findAll() {
    return this.entityManager.getRepository(TypePlan).find();
  }

  findOne(id: number) {
    return this.entityManager
      .getRepository(TypePlan)
      .findOne({ where: { id } });
  }

  async selectSkillToType(selectSkillsDto: SelectSkillsDto) {
    const typePlan = await this.entityManager.getRepository(TypePlan).findOne({
      where: { id: selectSkillsDto.typeId },
    });
    if (!typePlan) {
      throw new NotFoundException(
        `Type Plan with id ${selectSkillsDto.typeId} not found`,
      );
    }

    try {
      typePlan.skills = await Promise.all(
        selectSkillsDto.skillIds.map((skillId) =>
          this.entityManager.getRepository(Skill).findOneByOrFail({
            id: skillId,
            career_type:
              //
              typePlan.name == PlanType.POSITION ? null : typePlan.name, // skill ต้องจับคู่แค่กับ type plan ที่มี type ตรงกันอย่างเดียวเหรอ หรือ skill ที่ไม่มี type สามารถอยู่ได้ทุก type plan (โค้ดแบบนี้ไปก่อนละกัน)
          }),
        ),
      );
    } catch (error) {
      // Log ข้อมูล error อย่างละเอียดที่ server
      console.error('Error in selectSkillToType', {
        error: error.message,
        stack: error.stack,
        context: { typeId: selectSkillsDto.typeId },
      });

      // ส่งเฉพาะข้อความที่จำเป็นกลับไปที่ frontend
      throw new BadRequestException(
        'ไม่สามารถเพิ่มทักษะได้ กรุณาลองใหม่อีกครั้ง',
      );
    }

    return await this.entityManager.save(typePlan);
  }

  async removeSkillFromType(removeSkillDto: RemoveSkillDto) {
    const { typeId, skillId } = removeSkillDto;
    const typePlan = await this.entityManager.getRepository(TypePlan).findOne({
      where: { id: typeId },
      relations: { skills: true },
      select: { id: true, skills: { id: true } },
    });
    if (!typePlan) {
      throw new NotFoundException(`Type Plan with id ${typeId} not found`);
    }

    if (!typePlan.skills || typePlan.skills.length === 0) {
      throw new BadRequestException('ไม่มีทักษะใน type plan');
    }

    try {
      // remove skill from typePlan
      typePlan.skills = typePlan.skills.filter((skill) => skill.id !== skillId);
      return await this.entityManager.save(typePlan);
    } catch (error) {
      // Log ข้อมูล error อย่างละเอียดที่ server
      console.error('Error in removeSkillFromType', {
        error: error.message,
        stack: error.stack,
        context: {
          typeId,
          skillId,
        },
      });

      // ส่งเฉพาะข้อความที่จำเป็นกลับไปที่ frontend
      throw new BadRequestException('ไม่สามารถลบทักษะได้ กรุณาลองใหม่อีกครั้ง');
    }
  }
}
