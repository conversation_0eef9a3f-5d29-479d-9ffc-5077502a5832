import { IsArray, IsInt } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

// TypeSkillsDto
export class SelectSkillsDto {
  @IsInt()
  @ApiProperty({ example: 1 })
  typeId: number;

  @IsArray()
  @ApiProperty({ example: [1, 2, 3] })
  skillIds: number[];
}

// RemoveSkillDto
export class RemoveSkillDto {
  @IsInt()
  @ApiProperty({ example: 1 })
  typeId: number;

  @IsInt()
  @ApiProperty({ example: 1 })
  skillId: number;
}
