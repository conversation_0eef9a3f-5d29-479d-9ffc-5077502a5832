import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  JoinC<PERSON><PERSON>n,
  OneToMany,
} from 'typeorm';
import { AgeWorkCriteria } from './age-work-criteria.entity';
import { TypePlan } from '../../type-plans/entities/type-plan.entity';

@Entity('IDP_AGE_WORK')
export class AgeWork {
  @PrimaryGeneratedColumn({ name: 'ID' })
  id: number;

  @Column({ name: 'NAME', type: 'varchar', length: 255 })
  name: string;

  @Column({ name: 'START_YEAR', type: 'int' })
  startYear: number;

  @Column({ name: 'END_YEAR', type: 'int' })
  endYear: number;

  @Column({ name: 'AGE_WORK_CRITERIA_ID', type: 'int' })
  ageWorkCriteriaId: number;

  @ManyToOne(() => AgeWorkCriteria, (criteria) => criteria.ageWorks)
  @JoinColumn({ name: 'AGE_WORK_CRITERIA_ID' })
  ageWorkCriteria: AgeWorkCriteria;

  @OneToMany(() => TypePlan, (typePlan) => typePlan.ageWork)
  typePlans: TypePlan[];
}
