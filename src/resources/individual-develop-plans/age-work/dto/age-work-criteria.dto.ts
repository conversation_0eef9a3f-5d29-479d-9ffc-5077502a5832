import { IsNotEmpty, IsString, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAgeWorkCriteriaDto {
  @ApiProperty({
    description: 'Name of the age work criteria plan',
    example: 'แผนงานประจำปี 2567',
  })
  @IsNotEmpty()
  @IsString()
  name: string;
}

export class UpdateAgeWorkCriteriaDto {
  @ApiProperty({
    description: 'Name of the age work criteria plan',
    example: 'แผนงานประจำปี 2568',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;
}
