import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgeWorkController } from './age-work.controller';
import { AgeWorkCriteriaController } from './age-work-criteria.controller';
import { AgeWorkService } from './age-work.service';
import { AgeWork } from './entities/age-work.entity';
import { AgeWorkCriteria } from './entities/age-work-criteria.entity';
import { DevelopmentPlan } from '../development-plans/entities/development-plan.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([AgeWork, AgeWorkCriteria, DevelopmentPlan]),
  ],
  controllers: [AgeWorkController, AgeWorkCriteriaController],
  providers: [AgeWorkService],
  exports: [AgeWorkService],
})
export class AgeWorkModule {}
