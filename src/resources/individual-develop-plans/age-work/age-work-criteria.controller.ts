import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AgeWorkService } from './age-work.service';
import {
  CreateAgeWorkCriteriaDto,
  UpdateAgeWorkCriteriaDto,
} from './dto/age-work-criteria.dto';
import type { DataParams, DataResponse } from 'src/types/params';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import { AgeWorkCriteria } from './entities/age-work-criteria.entity';
import { AgeWork } from './entities/age-work.entity';

@ApiBearerAuth()
@ApiTags('Age Work Criteria')
@Controller('age-work-criteria')
export class AgeWorkCriteriaController {
  constructor(private readonly ageWorkService: AgeWorkService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new age work criteria' })
  @ApiBody({ type: CreateAgeWorkCriteriaDto })
  @ApiResponse({
    status: 201,
    description: 'Age work criteria successfully created',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createDto: CreateAgeWorkCriteriaDto) {
    return this.ageWorkService.createAgeWorkCriteria(createDto);
  }

  @Get()
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all age work criteria' })
  @ApiResponse({ status: 200, description: 'Return all age work criteria' })
  findAll(@Query() query: DataParams): Promise<DataResponse<AgeWorkCriteria>> {
    return this.ageWorkService.findAllAgeWorkCriteria(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an age work criteria by ID' })
  @ApiParam({ name: 'id', description: 'Age work criteria ID' })
  @ApiResponse({ status: 200, description: 'Return the age work criteria' })
  @ApiResponse({ status: 404, description: 'Age work criteria not found' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.ageWorkService.findOneAgeWorkCriteria(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an age work criteria' })
  @ApiParam({ name: 'id', description: 'Age work criteria ID' })
  @ApiBody({ type: UpdateAgeWorkCriteriaDto })
  @ApiResponse({
    status: 200,
    description: 'Age work criteria successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Age work criteria not found' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateAgeWorkCriteriaDto,
  ) {
    return this.ageWorkService.updateAgeWorkCriteria(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an age work criteria' })
  @ApiParam({ name: 'id', description: 'Age work criteria ID' })
  @ApiResponse({
    status: 200,
    description: 'Age work criteria successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Age work criteria not found' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.ageWorkService.removeAgeWorkCriteria(id);
  }

  @Get(':id/age-works')
  @DefaultQueryParams()
  @ApiOperation({ summary: 'Get all age works by criteria ID' })
  @ApiParam({ name: 'id', description: 'Age work criteria ID' })
  @ApiResponse({
    status: 200,
    description: 'Return all age works for the criteria',
  })
  @ApiResponse({ status: 404, description: 'Age work criteria not found' })
  findAgeWorksByCriteria(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ): Promise<DataResponse<AgeWork>> {
    return this.ageWorkService.findAgeWorkByCriteria(id, query);
  }
}
