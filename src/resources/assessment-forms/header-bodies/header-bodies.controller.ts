import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { HeaderBodiesService } from './header-bodies.service';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import type { CreateHeaderBodyDto } from '../dto/creates/create-header-body.dto';
import { UpdateHeaderBodyDto } from '../dto/updates/update-header-body.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

@ApiTags('Header-Bodies')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('header-bodies')
export class HeaderBodiesController {
  constructor(private readonly headerBodiesService: HeaderBodiesService) {}

  @Post()
  @ApiOperation({
    summary: 'สร้าง Header Bodyใหม่',
    description: 'สร้าง Header Body ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับสร้าง Header Body ใหม่',
    schema: {
      type: 'object',
      properties: {
        itemBlockId: { type: 'integer', example: 1 },
        title: { type: 'string', example: 'หัวข้อที่ 1' },
        description: { type: 'string', example: 'คำอธิบายหัวข้อที่ 1' },
      },
      required: ['itemBlockId'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createHeaderBodyDto: CreateHeaderBodyDto) {
    return this.headerBodiesService.createHeaderBody(
      createHeaderBodyDto.itemBlockId,
    );
  }

  @Get()
  findAll() {
    return this.headerBodiesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.headerBodiesService.findOne(+id);
  }

  @Patch(':id')
  @UseInterceptors(AnyFilesInterceptor())
  async update(@Param('id') id: string, @Body() body: any) {
    const dto = plainToInstance(UpdateHeaderBodyDto, body);
    const errors = await validate(dto);
    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.headerBodiesService.update(+id, dto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.headerBodiesService.remove(+id);
  }
}
