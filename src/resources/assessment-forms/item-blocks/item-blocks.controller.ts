import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseInterceptors,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiProperty,
  ApiTags,
} from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { CreateItemBlockDto } from '../dto/creates/create-item-block.dto';
import { UpdateItemBlockDto } from '../dto/updates/update-item-block.dto';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { ItemBlocksService } from './item-blocks.service';
import { BulkUpdateItemBlockSequencesDto } from '../dto/updates/ีupdate-block-sequence.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

@ApiTags('Assessment-Item-Blocks')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('item-blocks')
export class ItemBlocksController {
  constructor(private readonly itemBlocksService: ItemBlocksService) {}

  @Post('block')
  @UseInterceptors(AnyFilesInterceptor())
  async create(@Body() body: any) {
    const dto = plainToInstance(CreateItemBlockDto, body); // สำคัญ
    const errors = await validate(dto); // ตรวจสอบด้วย class-validator
    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.itemBlocksService.createBlock(dto);
  }

  async findAll(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
    @Query('page', ParseIntPipe) page: number,
  ) {
    return this.itemBlocksService.findAll(assessmentId, page);
  }

  @Get(':assessmentId/block')
  async findOne(@Param('assessmentId', ParseIntPipe) assessmentId: number) {
    return this.itemBlocksService.findOne(assessmentId);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'อัพเดท Item Block ใหม่',
    description: 'อัพเดท Item Block ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiProperty({
    type: UpdateItemBlockDto,
    description: 'ข้อมูลแก้ไข Item Block ใหม่',
  })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id') id: string,
    @Body() updateItemBlockDto: UpdateItemBlockDto,
  ) {
    return this.itemBlocksService.updateOne(+id, updateItemBlockDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.itemBlocksService.removeOne(id);
  }

  @Get(':id')
  async findItemOne(@Param('id', ParseIntPipe) itemBlockId: number) {
    return this.itemBlocksService.findItemOne(itemBlockId);
  }

  @Patch('update/sequences')
  updateSequences(@Body() updateSequencesDto: BulkUpdateItemBlockSequencesDto) {
    console.log(updateSequencesDto);
    return this.itemBlocksService.bulkUpdateSequences(updateSequencesDto);
  }

  @Post(':sourceId/duplicate')
  @ApiOperation({
    summary: 'Duplicate an existing Item Block',
    description:
      'Creates a complete copy of an existing Item Block with all its content (atomic operation)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Data for duplicating an Item Block',
    schema: {
      type: 'object',
      properties: {
        assessmentId: { type: 'integer', example: 1 },
        sequence: { type: 'integer', example: 2 },
        section: { type: 'integer', example: 1 },
      },
      required: ['assessmentId'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  duplicateBlock(
    @Param('sourceId', ParseIntPipe) sourceId: number,
    @Body()
    duplicateData: {
      assessmentId: number;
      sequence?: number;
      section?: number;
    },
  ) {
    return this.itemBlocksService.duplicateBlock(sourceId, duplicateData);
  }

  @Get('quiz/sequence/:submissionId/:sequence')
  sequenceQuestion(
    @Param('submissionId') submissionId: number,
    @Param('sequence') sequence: number,
  ) {
    return this.itemBlocksService.sequenceQuestion(submissionId, sequence);
  }
}
