import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { Assessment } from './assessment.entity';
import { Option } from './option.entity';
import { Question } from './question.entity';
import { HeaderBody } from './header-body.entity';
import { ImageBody } from './image-body.entity';

@Entity('ASM_ITEM_BLOCKS')
export class ItemBlock {
  @PrimaryGeneratedColumn({ name: 'IBL_ID' })
  id: number;

  @Column({ name: 'IBL_SEQUENCE' })
  sequence: number;

  @Column({ name: 'IBL_SECTION', default: 1 })
  section: number;

  @Column({
    name: 'IBL_TYPE',
    type: 'enum',
    enum: ItemBlockType,
  })
  type: ItemBlockType;

  @Column({ name: 'IBL_IS_REQUIRED', default: false })
  isRequired: boolean;

  @Column({ name: 'ASM_ID', nullable: true })
  assessmentId: number;

  @ManyToOne(() => Assessment, (assessment) => assessment.itemBlocks, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'ASM_ID' })
  assessment: Assessment;

  @OneToOne(() => HeaderBody, (headerBody) => headerBody.itemBlock, {
    cascade: true,
  })
  headerBody: HeaderBody;

  @OneToOne(() => ImageBody, (imageBody) => imageBody.itemBlock, {
    cascade: true,
    nullable: true,
  })
  imageBody: ImageBody | null;

  @OneToMany(() => Question, (question) => question.itemBlock, {
    cascade: true,
  })
  questions: Question[];

  @OneToMany(() => Option, (option) => option.itemBlock, {
    cascade: true,
  })
  options: Option[];
}
