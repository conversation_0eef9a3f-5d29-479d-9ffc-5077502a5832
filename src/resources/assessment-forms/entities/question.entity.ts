import { Type } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';
import { Response } from './response.entity';

@Entity('ASM_QUESTIONS')
export class Question {
  @PrimaryGeneratedColumn({ name: 'QST_ID' })
  id: number;

  @Column({ name: 'IBL_ID' })
  itemBlockId: number;

  @Column({ name: 'QST_QUESTION_TEXT', type: 'text' })
  questionText: string;

  @Column({ name: 'QST_IMAGE_PATH', nullable: true, type: 'text' })
  imagePath: string;

  @Column({ name: 'QST_IMAGE_WIDTH', nullable: true })
  imageWidth: number | null;

  @Column({ name: 'QST_IMAGE_HEIGHT', nullable: true })
  imageHeight: number | null;

  @Column({ name: 'QST_IS_HEADER', default: false })
  isHeader: boolean;

  @Column({ name: 'QST_SEQUENCE' })
  sequence: number;

  @Column({ name: 'QST_SIZE_LIMIT', nullable: true, default: null })
  sizeLimit: number;

  @Column({ name: 'QST_ACCEPT_FILE', nullable: true, default: null })
  acceptFile: string;

  @Column({ name: 'QST_UPLOAD_LIMIT', nullable: true, default: null })
  uploadLimit: number;

  @ManyToOne(() => ItemBlock, (itemBlock) => itemBlock.questions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;

  @OneToMany(() => Response, (response) => response.question, {
    cascade: true,
  })
  responses: Response[];

  @Column({ nullable: true })
  score: number | null;
}
