import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { Submission } from './submission.entity';
import { Option } from './option.entity';
import { Question } from './question.entity';

@Entity('ASM_RESPONSES')
export class Response {
  @PrimaryGeneratedColumn({ name: 'RES_ID' })
  id: number;

  @Column({ name: 'SBM_ID', nullable: false })
  submissionId: number;

  @Column({ name: 'OPT_ID', nullable: true })
  selectedOptionId: number | null;

  @Column({ name: 'QST_ID' })
  questionId: number;

  @ManyToOne(() => Submission, (submission) => submission.responses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'SBM_ID' })
  submission: Submission;

  @ManyToOne(() => Option, (option) => option.responses, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'OPT_ID' })
  selectedOption: Option | null;

  @ManyToOne(() => Question, (question) => question.responses, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'QST_ID' })
  question: Question;
}
