import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>umn,
  OneToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';

@Entity('ASM_HEADER_BODIES')
export class HeaderBody {
  @PrimaryGeneratedColumn({ name: 'HDB_ID' })
  id: number;

  @Column({ name: 'HDB_TITLE' })
  title: string;

  @Column({ name: 'HDB_DESCRIPTION', nullable: true })
  description: string | null;

  @Column({ name: 'IBL_ID' })
  itemBlockId: number;

  @OneToOne(() => ItemBlock, (itemBlock) => itemBlock.headerBody, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;
}
