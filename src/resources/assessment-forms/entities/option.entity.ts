import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';
import { Response } from './response.entity';

@Entity('ASM_OPTIONS')
export class Option {
  @PrimaryGeneratedColumn({ name: 'OPT_ID' })
  id: number;

  @Column({ name: 'IBL_ID' })
  itemBlockId: number;

  @Column({ name: 'OPT_OPTION_TEXT', type: 'text' })
  optionText: string;

  @Column({ name: 'OPT_IMAGE_PATH', type: 'text', nullable: true })
  imagePath: string | null;

  @Column({ name: 'OPT_VALUE', default: 1 })
  value: number;

  @Column({ name: 'OPT_SEQUENCE' })
  sequence: number;

  @Column({ name: 'OPT_NEXT_SECTION', nullable: true, default: null })
  nextSection: number;

  @ManyToOne(() => ItemBlock, (itemBlock) => itemBlock.options, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;

  @OneToMany(() => Response, (response) => response.selectedOption)
  responses: Response[];
}
