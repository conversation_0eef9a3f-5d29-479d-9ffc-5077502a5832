import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { Response } from './response.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('ASM_SUBMISSIONS')
export class Submission {
  @PrimaryGeneratedColumn({ name: 'SBM_ID' })
  id: number;

  @Column({
    name: 'SBM_START_AT',
    type: 'datetime',
    comment: 'เริ่มทำ',
  })
  startAt: Date;

  @Column({
    name: 'SBM_END_AT',
    type: 'datetime',
    comment: 'หมดเวลา',
    nullable: true,
  })
  endAt: Date;

  @Column({
    name: 'SBM_SUBMIT_AT',
    type: 'datetime',
    nullable: true,
    comment: 'วันที่ส่ง',
  })
  submitAt: Date | null;

  @Column({ name: 'USR_ID' })
  userId: number;

  @Column({ name: 'ASM_ID' })
  assessmentId: number;

  @ManyToOne(() => User, (user) => user.submissions)
  @JoinColumn({ name: 'USR_ID' })
  user: User;

  @ManyToOne(() => Assessment, (assessment) => assessment.submissions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'ASM_ID' })
  assessment: Assessment;

  @OneToMany(() => Response, (response) => response.submission, {
    cascade: true,
  })
  responses: Response[];
}
