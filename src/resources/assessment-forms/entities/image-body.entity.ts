import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Colum<PERSON>,
  OneTo<PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';

@Entity('ASM_IMAGE_BODIES')
export class ImageBody {
  @PrimaryGeneratedColumn({ name: 'IMB_ID' })
  id: number;

  @Column({ name: 'IMB_IMAGE_TEXT', nullable: true })
  imageText: string | null;

  @Column({ name: 'IMB_IMAGE_PATH', nullable: true, type: 'text' })
  imagePath: string | null;

  @Column({ name: 'IMB_IMAGE_WIDTH', nullable: true })
  imageWidth: number | null;

  @Column({ name: 'IMB_IMAGE_HEIGHT', nullable: true })
  imageHeight: number | null;

  @Column({ name: 'IBL_ID' })
  itemBlockId: number;

  @OneToOne(() => ItemBlock, (itemBlock) => itemBlock.imageBody, {
    onDelete: 'CASCADE',
  })
  @Join<PERSON>olumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;
}
