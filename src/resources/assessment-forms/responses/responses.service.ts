import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';
import type { UpdateResponseDto } from '../dto/updates/update-response.dto';
import type { CreateResponseDto } from '../dto/creates/create-response.dto';
import { ResponsesHelper } from './responses.helper';

@Injectable()
export class ResponsesService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
    private responsesHelper: ResponsesHelper,
  ) {}

  async create(createResponseDto: CreateResponseDto): Promise<Response> {
    const response = this.responseRepo.create(createResponseDto);
    return await this.responseRepo.save(response);
  }

  findAll() {
    return `This action returns all responses`;
  }

  findOne(id: number) {
    return `This action returns a #${id} response`;
  }

  async findEvaluateAnswer(
    submission: number,
    question: number,
  ): Promise<Response | {}> {
    const response = await this.responseRepo.findOne({
      where: { submissionId: submission, questionId: question },
      relations: ['question', 'selectedOption'],
    });

    return response || {}; // ถ้าไม่เจอจะคืน {}
  }

  async findEvaluateAnswers(
    submissionId: number,
    questionId: number,
  ): Promise<Response[]> {
    return this.responseRepo.find({
      where: { submissionId, questionId },
      relations: ['question', 'selectedOption'],
    });
  }

  async findEvaluateCheckBoxAnswer(
    submission: number,
    question: number,
    selectedOption: number,
  ) {
    const responese = await this.responseRepo.findOne({
      where: {
        submissionId: submission,
        questionId: question,
        selectedOptionId: selectedOption,
      },
    });

    return responese ?? {}; // ถ้าไม่เจอให้คืน {}
  }

  findAllBySubmissionId(submissionId: number) {
    return this.responseRepo.find({
      where: { submissionId },
      relations: { selectedOption: true },
    });
  }

  async update(
    id: number,
    updateResponseDto: UpdateResponseDto,
  ): Promise<Response> {
    await this.responseRepo.update(id, updateResponseDto);
    return await this.responseRepo.findOneOrFail({
      where: { id },
      relations: ['submission', 'selectedOption', 'question'],
    });
  }

  async remove(id: number): Promise<void> {
    await this.responseRepo.delete(id);
  }

  async clear(id: number): Promise<void> {
    console.log('service', id);
    await this.responseRepo.delete({ id });
  }

  async userSaveQuizResponse(createResponseDto: CreateResponseDto) {
    const {
      submissionId,
      questionId,
      answerText,
      selectedOptionId,
      responseId,
      selectedOptionIds, // สำหรับ CHECKBOX (array of option IDs)
    } = createResponseDto;

    // ✅ Validate submission
    await this.responsesHelper.validateSubmission(submissionId);

    // ✅ Get question with itemBlock type
    const question = await this.entityManager
      .getRepository('Question')
      .createQueryBuilder('question')
      .leftJoinAndSelect('question.itemBlock', 'itemBlock')
      .where('question.id = :questionId', { questionId })
      .getOne();

    if (!question) {
      throw new NotFoundException(`Question with ID ${questionId} not found`);
    }

    const itemBlockType = question.itemBlock?.type;

    // ✅ Handle different itemBlock types
    switch (itemBlockType) {
      case 'CHECKBOX':
        return await this.handleCheckboxResponse(
          submissionId,
          questionId,
          selectedOptionIds || [],
        );

      case 'TEXTFIELD':
        return await this.handleTextFieldResponse(
          submissionId,
          questionId,
          answerText,
          selectedOptionId,
          responseId,
        );

      case 'RADIO':
      default:
        return await this.handleSingleSelectResponse(
          submissionId,
          questionId,
          selectedOptionId,
          responseId,
        );
    }
  }

  // ✅ Handle CHECKBOX - Clear all existing and save new selections
  private async handleCheckboxResponse(
    submissionId: number,
    questionId: number,
    selectedOptionIds: number[],
  ) {
    // Clear all existing responses for this question
    await this.responseRepo.delete({
      submissionId,
      questionId,
    });

    // Save new selections
    const savedResponses = [];
    for (const optionId of selectedOptionIds) {
      const newResponse = this.responseRepo.create({
        submissionId,
        questionId,
        selectedOptionId: optionId,
      });
      const savedResponse = await this.responseRepo.save(newResponse);
      savedResponses.push(savedResponse);
    }

    return savedResponses;
  }

  // ✅ Handle TEXTFIELD
  private async handleTextFieldResponse(
    submissionId: number,
    questionId: number,
    answerText: string,
    selectedOptionId: number | null,
    responseId: number | null,
  ) {
    let finalSelectedOptionId = selectedOptionId;
    if (answerText) {
      finalSelectedOptionId =
        await this.responsesHelper.saveOrUpdateOptionForTextAnswer(
          answerText,
          selectedOptionId,
          questionId,
        );
    }

    // Clear existing response first (for textfield auto-save)
    if (!responseId) {
      await this.responseRepo.delete({
        submissionId,
        questionId,
      });
    }

    return await this.responsesHelper.saveOrUpdateResponse(
      submissionId,
      questionId,
      finalSelectedOptionId,
      responseId,
    );
  }

  // ✅ Handle RADIO/DROPDOWN - Single selection
  private async handleSingleSelectResponse(
    submissionId: number,
    questionId: number,
    selectedOptionId: number | null,
    responseId: number | null,
  ) {
    // Clear existing response first (for single select)
    if (!responseId) {
      await this.responseRepo.delete({
        submissionId,
        questionId,
      });
    }

    return await this.responsesHelper.saveOrUpdateResponse(
      submissionId,
      questionId,
      selectedOptionId,
      responseId,
    );
  }
}
