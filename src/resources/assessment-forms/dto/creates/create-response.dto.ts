import {
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsString,
  <PERSON>A<PERSON>y,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { Type } from 'class-transformer';

export class CreateResponseDto {
  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number) // 👈 เพิ่มเพื่อแปลงเป็น number
  @IsNumber()
  submissionId: number;

  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number)
  @IsNumber()
  questionId: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  responseId?: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  selectedOptionId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  answerText?: string;

  @ApiProperty({
    required: false,
    type: [Number],
    description: 'Array of selected option IDs for CHECKBOX type questions',
  })
  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @IsNumber({}, { each: true })
  selectedOptionIds?: number[];
}
