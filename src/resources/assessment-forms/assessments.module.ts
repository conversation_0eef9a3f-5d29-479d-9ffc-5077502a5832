import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuizService } from './services/quiz.service';
import { AssessmentsController } from './assessments.controller';
import { Assessment } from './entities/assessment.entity';
import { AssessmentValidator } from './helper/assessments.validator';
import { AssessmentHelper } from './helper/assessments.helper';
import { ItemBlocksModule } from './item-blocks/item-blocks.module';
import { ItemBlock } from './entities/item-block.entity';
import { HeaderBody } from './entities/header-body.entity';
import { HeaderBodiesModule } from './header-bodies/header-bodies.module';
import { ImageBody } from './entities/image-body.entity';
import { Question } from './entities/question.entity';
import { QuestionsModule } from './questions/questions.module';
import { HeaderBodiesService } from './header-bodies/header-bodies.service';
import { ItemBlocksService } from './item-blocks/item-blocks.service';
import { Response } from './entities/response.entity';
import { ImageBodiesModule } from './image-bodies/image-bodies.module';
import { AssessmentsService } from './services/assessments.service';
import { ResponsesModule } from './responses/responses.module';
import { OptionsModule } from './options/options.module';
import { Option } from './entities/option.entity';
import { SubmissionsModule } from './submissions/submissions.module';
import { Submission } from './entities/submission.entity';
import { QuizDashboardService } from './services/quiz.dashboard.service';
import { UsersModule } from '../users/users.module';
import { EvaluateDashBoardService } from './services/evaluate.dashboard.service';
import { MockupDataService } from './services/mockup-data.service';
import { QuizHelperService } from './services/quiz-helper.service';
import { AuthModule } from 'src/auth/auth.module';
import { User } from '../users/entities/user.entity';
import { ApiModule } from 'src/api/api.module';
import { FileUploadModule } from './utils/file-upload.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Assessment,
      ItemBlock,
      HeaderBody,
      ImageBody,
      Question,
      Response,
      Option,
      Submission,
      User,
    ]),
    forwardRef(() => ItemBlocksModule),
    forwardRef(() => OptionsModule),
    HeaderBodiesModule,
    QuestionsModule,
    ImageBodiesModule, // Assuming ImageBodyModule is defined similarly to others
    ResponsesModule,
    SubmissionsModule,
    UsersModule,
    AuthModule,
    ApiModule,
    FileUploadModule,
  ],
  controllers: [AssessmentsController],
  providers: [
    QuizService,
    QuizDashboardService,
    EvaluateDashBoardService,
    HeaderBodiesService,
    ItemBlocksService,
    AssessmentValidator,
    AssessmentHelper,
    AssessmentsService,
    MockupDataService,
    QuizHelperService,
  ],
  exports: [
    QuizService,
    QuizDashboardService,
    EvaluateDashBoardService,
    ItemBlocksService,
    HeaderBodiesService,
    AssessmentValidator,
    AssessmentHelper,
    AssessmentsService,
    QuizHelperService,
  ],
})
export class AssessmentsModule {}
