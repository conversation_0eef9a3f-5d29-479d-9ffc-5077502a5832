import type { RouteRecordRaw } from 'vue-router';

const devRoutes: RouteRecordRaw[] = [
  {
    path: '/developments',
    name: 'developments',
    component: () => import('../layouts/MainLayout.vue'),
    children: [
      {
        path: 'management',
        name: 'Development Plan Management',
        component: () => import('../pages/dev-plan/DevPlanManagement.vue'),
      },
      {
        path: 'planing',
        name: 'Development Planing',
        component: () => import('../pages/dev-plan/DevPlaning.vue'),
      },
      {
        path: 'planing/:id',
        name: 'dev-plan-planning',
        component: () => import('../pages/dev-plan/DevPlaning.vue'),
      },
      {
        path: 'my-plan',
        name: 'My development plan',
        component: () => import('../pages/dev-plan/MyDevelopmentPlan.vue'),
      },
    ],
  },
];

export default devRoutes;
