import type { RouteRecordRaw } from 'vue-router';

const idpRoutes: RouteRecordRaw[] = [
  {
    path: '/idp',
    name: 'idp',
    component: () => import('../layouts/MainLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/idp') {
        next({ name: 'idp-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'idp-management',
        component: () => import('../pages/idp/IdpManagementPage.vue'),
      },
      {
        path: 'age-work-criteria',
        name: 'idp-age-work-management',
        component: () => import('../pages/idp/AgeWorkManagementPage.vue'),
      },
      {
        path: 'age-work-criteria/:criteriaId/age-works',
        name: 'idp-age-work',
        component: () => import('../pages/idp/AgeWorkPage.vue'),
      },
    ],
  },
];

export default idpRoutes;
