import type { RouteRecordRaw } from 'vue-router';
import { getAllowedSectionsForUser } from 'src/utils/sequenceHelper';

const evaluateRoutes: RouteRecordRaw[] = [
  {
    path: '/evaluate',
    name: 'evaluate',
    component: () => import('../../layouts/MainLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/evaluate') {
        next({ name: 'evaluate-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'evaluate-management',
        component: () => import('../../pages/evaluate/EvaluateManagementPage.vue'),
        meta: {},
      },
      {
        path: ':id(\\d+)/edit',
        name: 'evaluate-edit',
        component: () => import('../../pages/evaluate/EvaluateEditorPage.vue'),
        props: true,
      },
      {
        path: ':id/:section/preview',
        name: 'evaluate-preview',
        component: () => import('../../pages/evaluate/EvaluateIdPage.vue'),
        props: true,
        meta: {
          status: 'preview',
        },
      },
      {
        path: 'do/:url/:section',
        name: 'evaluate-do',
        component: () => import('../../pages/evaluate/EvaluateIdPage.vue'),
        props: true,
        meta: {
          status: 'do',
          id: 0,
        },
        beforeEnter: (to, from, next) => {
          try {
            const allowedSections = getAllowedSectionsForUser();
            const currentSection = Number(to.params.section);

            if (allowedSections.includes(currentSection)) {
              next();
            } else {
              // redirect กลับไปยัง section ล่าสุดที่ user มีสิทธิ์
              const lastAllowed = Math.max(...allowedSections);
              next({
                name: 'evaluate-do',
                params: {
                  url: to.params.url,
                  section: lastAllowed.toString(),
                },
              });
            }
          } catch (error) {
            console.error('beforeEnter error:', error);
            next(false);
          }
        },
      },
      {
        path: 'user',
        name: 'evaluate-user',
        component: () => import('../../pages/UserAssessmentPage.vue'),
        props: { type: 'evaluate' },
      },
    ],
  },
];

export default evaluateRoutes;
