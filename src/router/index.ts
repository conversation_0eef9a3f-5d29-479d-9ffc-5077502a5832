import { createRouter, createWebHistory } from 'vue-router';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';
import umsRoutes from './ums';
import quizRoutes from './quiz';
import evaluateRoutes from './evaluate';
import { useAuthStore } from '../stores/auth';
import { jwtDecode, type JwtPayload } from 'jwt-decode';
import idpRoutes from './route_idp';
import inHouseRoutes from './route_inhouse';
import skillRoutes from './route_skill';
import competencyRoutes from './route_competency';
import devRoutes from './route_dev_plan';
import monitorRoutes from './route_monitor';

// ✅ ฟังก์ชันเช็คว่า token หมดอายุหรือยัง
function isTokenExpired(token: string): boolean {
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    if (!decoded.exp) return true;
    return decoded.exp * 1000 < Date.now(); // exp เป็นวินาที, Date.now() เป็นมิลลิวินาที
  } catch {
    return true;
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      component: () => import('layouts/FullLayout.vue'),
      children: [{ path: '', name: 'login', component: () => import('../pages/LoginPage.vue') }],
    },
    {
      path: '/',
      component: () => import('src/layouts/MainLayout.vue'),
      children: [
        {
          path: '', // Empty path means this is the default child route
          redirect: { name: 'home' }, // Redirect to the home page
        },
        {
          path: 'home',
          name: 'home',
          component: () => import('../pages/HomePage.vue'),
        },
      ],
    },
    ...umsRoutes,
    ...quizRoutes,
    ...evaluateRoutes,
    ...idpRoutes,
    ...inHouseRoutes,
    ...competencyRoutes,
    ...skillRoutes,
    ...devRoutes,
    ...monitorRoutes,
    {
      path: '/test',
      component: () => import('src/layouts/MainLayout.vue'),
      children: [
        {
          path: '',
          name: 'test',
          component: () => import('pages/TestPage.vue'),
        },
      ],
    },
    {
      path: '/:catchAll(.*)*',
      component: () => import('pages/ErrorNotFound.vue'),
    },
  ],
});

// // ✅ เช็คว่าผู้ใช้มีสิทธิ์ใดๆ หรือไม่
// const isHavePerms = (user: User): boolean => {
//   return Array.isArray(user?.psnPermissions) && user.psnPermissions.length > 0;
// };

// ✅ Global Route Guard
router.beforeEach(
  (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const authStore = useAuthStore();
    const token = localStorage.getItem('access_token');

    // 🔐 ตรวจสอบว่า token หมดอายุหรือยัง
    if (token && isTokenExpired(token)) {
      authStore.logout(); // ล้าง token และ redirect ไป login
      return;
    }

    // If accessing login page, allow it without further checks
    if (to.name === 'login') {
      // If user is already logged in and trying to access login, redirect to home
      if (token && !isTokenExpired(token)) {
        next({ name: 'home' });
        return;
      }
      next();
      return;
    }

    localStorage.setItem('acs', 'Y');

    if (to.meta.acs) {
      // หากเข้าผ่านลิงก์พิเศษ เช่น email link
      localStorage.setItem('acs', 'Y');
      next();
    } else if (!authStore.isLoggedIn && to.name !== 'login' && to.name !== 'at-test') {
      localStorage.setItem('acs', 'N');

      // ✅ เพิ่มบรรทัดนี้เพื่อเก็บ path ที่ user พยายามเข้าก่อน login
      localStorage.setItem('redirectAfterLogin', to.fullPath);

      next({ name: 'login' });
    } else {
      localStorage.setItem('acs', 'N');
      // Allow access even if user.psnPermissions is empty
      // if (to.name !== 'login' && !isHavePerms(user!)) {
      //   next({ name: 'login' });
      //   authStore.showNotifyDialog('ไม่พบสิทธิ์การใช้งาน');
      // }
      if (to.meta.perms) {
        // Check if user is Super Admin - if so, allow access to all routes
        if (authStore.isSuperAdmin()) {
          next();
        } else {
          // ใช้ helper function จาก store
          if (authStore.hasAnyPermission(to.meta.perms as number[])) {
            next();
          } else {
            next({ name: 'home' });
          }
        }
      } else {
        next();
      }
    }
  },
);

export default router;
