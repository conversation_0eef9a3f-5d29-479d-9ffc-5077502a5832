<!-- <template>
  <div class="flex bg-grey-2 sub-body" style="padding: 0 4px">
    <q-tabs active-color="primary" inline-label v-model="selectedTab" outside-arrows>
      <q-tab
        class="cus-tab"
        v-for="m in filteredMenuItems"
        :key="m.title"
        :name="m.title"
        :label="m.title"
        :icon="m.icon || 'settings'"
        @click="navigateTo(m.link)"
      ></q-tab>
    </q-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useGlobalStore, type MenuType } from 'src/stores/global';
import { useAuthStore } from 'src/stores/auth';

const props = defineProps<{
  type: MenuType;
}>();

const router = useRouter();
const globalStore = useGlobalStore();
const authStore = useAuthStore();
const selectedTab = ref();

watch(
  () => props.type,
  (newType) => {
    globalStore.setActiveMenuType(newType);
  },
  { immediate: true },
);

const menuItems = computed(() => globalStore.getActiveMenuItems());

// Filter menu items based on user permissions
const filteredMenuItems = computed(() => {
  const user = authStore.getCurrentUser();
  
  // If user is Super Admin, show all menu items
  if (authStore.isSuperAdmin()) {
    return menuItems.value;
  }
  
  return menuItems.value.filter((item) => {
    // If no permissions required, show the item
    if (!item.perId || item.perId.length === 0) return true;

    // Check if user has any of the required permissions
    return user?.psnPermissions.some((perm) => item.perId?.includes(perm.perId));
  });
});

async function navigateTo(link: string) {
  if (link) {
    await router.push(link);
  }
}
</script>

<style scoped lang="scss">
.cus-tab {
  color: black;
  border-radius: 0px;
}
</style> -->

<!-- <template>
  <div class="flex bg-grey-2 sub-body" style="padding: 0 4px">
    <q-tabs active-color="primary" inline-label v-model="selectedTab" outside-arrows>
      <q-tab
        class="cus-tab"
        v-for="m in filteredMenuItems"
        :key="m.title"
        :name="m.title"
        :label="m.title"
        :icon="m.icon || 'settings'"
        @click="navigateTo(m.link)"
      ></q-tab>
    </q-tabs>
  </div>
</template> -->

<script setup lang="ts">
import { watch } from 'vue';
// import { useRouter } from 'vue-router';
import { useGlobalStore } from 'src/stores/global';
import type { MenuType } from 'src/types/app';
// import { useAuthStore } from 'src/stores/auth';

const props = defineProps<{
  type: MenuType;
}>();

// const router = useRouter();
const globalStore = useGlobalStore();
// const authStore = useAuthStore();
// const selectedTab = ref();

watch(
  () => props.type,
  (newType) => {
    globalStore.setActiveMenuType(newType);
  },
  { immediate: true },
);

// const menuItems = computed(() => globalStore.getActiveMenuItems());

// Filter menu items based on user permissions
// const filteredMenuItems = computed(() => {
//   const user = authStore.getCurrentUser();

//   return menuItems.value.filter((item) => {
//     // ถ้าเมนูนี้ไม่ต้องใช้ permission ใด ๆ
//     if (!item.perId || item.perId.length === 0) return true;

//     // ตรวจสอบว่าผู้ใช้มี permission ใด ๆ ที่ตรงกับ item.perId
//     return user?.role?.permissions?.some((perm) => item.perId!.includes(perm.id));
//   });
// });

// async function navigateTo(link: string) {
//   if (link) {
//     await router.push(link);
//   }
// }
</script>

<style scoped lang="scss">
.cus-tab {
  color: black;
  border-radius: 0px;
}
</style>
