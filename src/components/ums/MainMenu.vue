<!-- <script setup lang="ts">
import { ref, onMounted, onUpdated, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type User from 'src/types/ums/user';
import { useAuthStore } from 'src/stores/auth';

/**
 * Submenu item in the UMS menu tabs
 */
interface TabSubMenuItem {
  title: string;
  pathName: string; // Route name for programmatic navigation
  perId: number[]; // Permission IDs required to see this item
}

/**
 * Main tab item in the UMS menu
 */
interface TabItem {
  title: string;
  icon: string;
  pathName: string; // Route name for programmatic navigation
  perId: number[]; // Permission IDs required to see this item
  submenu: boolean; // Whether this tab has submenu items
  more: TabSubMenuItem[]; // Submenu items if submenu is true
}

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const user = ref<User | undefined>(undefined);
const activeTab = ref<string>('');
const activeSub = ref<string>('');

const tabList = ref<TabItem[]>([
  {
    title: 'จัดการข้อมูลสิทธิ์ในระบบ',
    icon: 'manage_accounts',
    pathName: 'ums_permission',
    perId: [1],
    submenu: false,
    more: [],
  },
  {
    title: 'จัดการข้อมูลส่วนงาน',
    icon: 'apartment',
    pathName: 'ums_faculty',
    perId: [1, 2],
    submenu: false,
    more: [],
  },
  {
    title: 'จัดการข้อมูลบุคลากร',
    icon: 'group',
    pathName: 'ums_fac_person',
    perId: [1, 2],
    submenu: false,
    more: [],
  },
  {
    title: 'จัดการ',
    icon: 'settings',
    pathName: '',
    perId: [1, 2],
    submenu: true,
    more: [
      { title: 'จัดการทดสอบ 0', pathName: 'ums_test0', perId: [1, 2] },
      { title: 'จัดการทดสอบ 1', pathName: 'ums_test1', perId: [1, 2] },
    ],
  },
]);

onMounted(() => {
  user.value = authStore.getCurrentUser();
  updateTabs();
});

onUpdated(() => updateTabs());

function updateTabs(): void {
  for (const tab of tabList.value) {
    if (tab.submenu) {
      for (const sub of tab.more) {
        if (route.name === sub.pathName) {
          activeTab.value = tab.title;
          activeSub.value = sub.title;
        }
      }
    } else {
      if (route.name === tab.pathName) {
        activeTab.value = tab.title;
        activeSub.value = '';
      }
    }
  }
}

const filteredTabs = computed<TabItem[]>(() => {
  // If user is Super Admin, show all tabs
  if (authStore.isSuperAdmin()) {
    return tabList.value;
  }
  
  return tabList.value.filter((tab) =>
    user.value?.psnPermissions.some((perm) => tab.perId.includes(perm.perId)),
  );
});

const filteredSubmenu = (tab: TabItem): TabSubMenuItem[] => {
  // If user is Super Admin, show all submenu items
  if (authStore.isSuperAdmin()) {
    return tab.more;
  }
  
  return tab.more.filter((sub) =>
    user.value?.psnPermissions.some((perm) => sub.perId.includes(perm.perId)),
  );
};
</script>

<template>
  <q-tabs v-model="activeTab" class="text-primary bg-grey-2 q-pa-sm" align="left">
    <template v-for="tab in filteredTabs" :key="tab.title">
      <q-tab
        :name="tab.title"
        :label="tab.title"
        :icon="tab.icon"
        @click="() => tab.pathName && router.push({ name: tab.pathName })"
      />
      <q-menu v-if="tab.submenu && activeTab === tab.title">
        <q-list style="min-width: 200px">
          <q-item
            v-for="sub in filteredSubmenu(tab)"
            :key="sub.title"
            clickable
            v-ripple
            @click="router.push({ name: sub.pathName })"
          >
            <q-item-section avatar>
              <q-icon name="more_horiz" />
            </q-item-section>
            <q-item-section>{{ sub.title }}</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </template>
  </q-tabs>
</template>

<style scoped></style> -->

<script setup lang="ts">
import { ref, onMounted, onUpdated, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type { User } from 'src/types/models';
import { useAuthStore } from 'src/stores/auth';

/**
 * Submenu item in the UMS menu tabs
 */
interface TabSubMenuItem {
  title: string;
  pathName: string; // Route name for programmatic navigation
  perId: number[]; // Permission IDs required to see this item
}

/**
 * Main tab item in the UMS menu
 */
interface TabItem {
  title: string;
  icon: string;
  pathName: string; // Route name for programmatic navigation
  perId: number[]; // Permission IDs required to see this item
  submenu: boolean; // Whether this tab has submenu items
  more: TabSubMenuItem[]; // Submenu items if submenu is true
}

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const user = ref<User | undefined>(undefined);
const activeTab = ref<string>('');
const activeSub = ref<string>('');

const tabList = ref<TabItem[]>([
  {
    title: 'จัดการข้อมูลสิทธิ์ในระบบ',
    icon: 'manage_accounts',
    pathName: 'ums_permission',
    perId: [1],
    submenu: false,
    more: [],
  },
  {
    title: 'จัดการข้อมูลส่วนงาน',
    icon: 'apartment',
    pathName: 'ums_faculty',
    perId: [1, 2],
    submenu: false,
    more: [],
  },
  {
    title: 'จัดการข้อมูลบุคลากร',
    icon: 'group',
    pathName: 'ums_fac_person',
    perId: [1, 2],
    submenu: false,
    more: [],
  },
  {
    title: 'จัดการ',
    icon: 'settings',
    pathName: '',
    perId: [1, 2],
    submenu: true,
    more: [
      { title: 'จัดการทดสอบ 0', pathName: 'ums_test0', perId: [1, 2] },
      { title: 'จัดการทดสอบ 1', pathName: 'ums_test1', perId: [1, 2] },
    ],
  },
]);

onMounted(() => {
  user.value = authStore.getCurrentUser();
  updateTabs();
});

onUpdated(() => updateTabs());

function updateTabs(): void {
  for (const tab of tabList.value) {
    if (tab.submenu) {
      for (const sub of tab.more) {
        if (route.name === sub.pathName) {
          activeTab.value = tab.title;
          activeSub.value = sub.title;
        }
      }
    } else if (route.name === tab.pathName) {
      activeTab.value = tab.title;
      activeSub.value = '';
    }
  }
}

const filteredTabs = computed<TabItem[]>(() =>
  tabList.value.filter((tab) =>
    user.value?.roles?.[0]?.permissions?.some((perm) => tab.perId.includes(perm.id)),
  ),
);

const filteredSubmenu = (tab: TabItem): TabSubMenuItem[] => {
  return tab.more.filter((sub) =>
    user.value?.roles?.[0]?.permissions?.some((perm) => sub.perId.includes(perm.id)),
  );
};
</script>

<template>
  <q-tabs v-model="activeTab" class="text-primary bg-grey-2 q-pa-sm" align="left">
    <template v-for="tab in filteredTabs" :key="tab.title">
      <q-tab
        :name="tab.title"
        :label="tab.title"
        :icon="tab.icon"
        @click="() => tab.pathName && router.push({ name: tab.pathName })"
      />
      <q-menu v-if="tab.submenu && activeTab === tab.title">
        <q-list style="min-width: 200px">
          <q-item
            v-for="sub in filteredSubmenu(tab)"
            :key="sub.title"
            clickable
            v-ripple
            @click="router.push({ name: sub.pathName })"
          >
            <q-item-section avatar>
              <q-icon name="more_horiz" />
            </q-item-section>
            <q-item-section>{{ sub.title }}</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </template>
  </q-tabs>
</template>
