<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card class="q-pa-md">
      <q-card-section class="q-pb-none row">
        <q-icon name="warning" class="col-12 q-mb-lg" size="xl" color="negative" />
        <div class="text-h6 text-bold col-12 text-center">{{ title }}</div>
      </q-card-section>

      <q-card-section class="q-pt-md">
        <slot>
          <div class="text-body1">คุณแน่ใจหรือไม่ ที่จะดำเนินการนี้ ?</div>
        </slot>
      </q-card-section>

      <q-card-actions align="center">
        <q-btn label="ยกเลิก" color="grey-9" id="cancel-btn" flat @click="cancel" />
        <q-btn
          label="ยืนยัน"
          class="text-bold"
          id="confirm-btn"
          color="negative"
          flat
          @click="confirm"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: boolean;
  title: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();

const isOpen = ref(props.modelValue);

watch(
  () => props.modelValue,
  (val) => {
    isOpen.value = val;
  },
);

watch(isOpen, (val) => {
  emit('update:modelValue', val);
});

const confirm = () => {
  emit('confirm');
  isOpen.value = false;
};

const cancel = () => {
  emit('cancel');
  isOpen.value = false;
};
</script>
<style scoped lang="scss">
#confirm-btn:hover {
  transition: all 0.15s ease-in-out;
}
#confirm-btn:hover {
  background-color: $negative;
  color: white !important;
}
</style>
