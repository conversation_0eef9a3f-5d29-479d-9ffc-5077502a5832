<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="container">
      <q-card-section>
        <div class="text-h6">{{ dialogTitle }}</div>
        <q-separator class="q-my-sm" />
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <q-input
              v-model="formDataRef.name"
              label="ชื่อเกณฑ์อายุการปฏิบัติงาน"
              :rules="[(val) => !!val || 'กรุณากรอกชื่อเกณฑ์อายุการปฏิบัติงาน']"
              outlined
            />
          </div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="ยกเลิก" @click="onDialogCancel" />
          <q-btn type="submit" color="primary" label="บันทึก" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogPluginComponent } from 'quasar';
import { ref, reactive, onMounted, computed } from 'vue';
import type { AgeWorkCriteria } from 'src/types/idp';

defineEmits([...useDialogPluginComponent.emits]);

const props = defineProps<{
  title?: string;
  formData?: AgeWorkCriteria;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const dialogTitle = computed(() => {
  return props.formData ? 'แก้ไขเกณฑ์อายุการปฏิบัติงาน' : 'สร้างเกณฑ์อายุการปฏิบัติงานใหม่';
});

const formRef = ref();
const formDataRef = reactive<Omit<AgeWorkCriteria, 'id'>>({
  name: '',
  ageWorks: [],
  developmentPlans: [],
});

onMounted(() => {
  if (props.formData) {
    formDataRef.name = props.formData.name;
  }
});

const submitForm = async () => {
  try {
    const isValid = await formRef.value.validate();
    if (isValid) {
      onDialogOK(formDataRef);
    }
  } catch (error) {
    console.error('Form validation error:', error);
  }
};
</script>

<style scoped>
.container {
  min-width: 400px;
}
</style>
