<template>
  <q-breadcrumbs separator=">">
    <q-breadcrumbs-el
      v-for="(item, index) in items"
      :key="index"
      :label="item.title"
      :icon="item.icon"
      :to="item.link"
      :disable="!item.link || index === items.length - 1"
      :class="index === items.length - 1 ? 'text-black' : 'text-grey-8'"
    />
  </q-breadcrumbs>
</template>

<script setup lang="ts">
import type { MenuLink } from 'src/types/app';

defineProps<{
  items: MenuLink[];
}>();
</script>
