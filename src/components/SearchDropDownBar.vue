<template>
  <q-select
    style="width: 325px"
    outlined
    dense
    use-input
    input-debounce="300"
    v-model="searchText"
    :options="filteredOptions"
    :placeholder="placeholder ?? 'ค้นหา'"
    class="q-mr-sm"
    @filter="filterFn"
    @update:model-value="onSelect"
    emit-value
    map-options
    clearable
  >
  </q-select>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue';

const props = defineProps<{ placeholder?: string; options?: string[] }>();
const emit = defineEmits<{ (e: 'search', value: string): void }>();

const searchText = ref('');
const filteredOptions = ref(props.options ?? []);

function filterFn(val: string, update: (cb: () => void) => void) {
  update(() => {
    if (!val) {
      filteredOptions.value = props.options ?? [];
    } else {
      filteredOptions.value = (props.options ?? []).filter((opt) =>
        opt.toLowerCase().includes(val.toLowerCase()),
      );
    }
  });
  emit('search', val);
}

function onSelect(val: string) {
  emit('search', val);
}
</script>
