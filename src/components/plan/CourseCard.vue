<template>
  <div
    style="
      width: 620px;
      min-width: 520px;
      border: 1px solid #dddddd;
      border-radius: 20px;
      padding: 24px;
      margin-top: 24px;
    "
  >
    <div class="title">หลักสูตรแนะนำ</div>
    <div class="chip-list">
      <div
        class="chip"
        :class="{ active: selectedCourse === item }"
        v-for="(item, idx) in courses"
        :key="idx"
        @click="selectCourse(item)"
      >
        <q-btn flat dense round icon="close" size="sm" class="close" @click.stop="closeDetail" />
        <span class="chip-label">{{ item }}</span>
      </div>
    </div>
    <div
      v-if="
        selectedCourse &&
        selectedCourse !== 'ส่งหลักฐานการอบรมจากภายนอก' &&
        selectedCourse !== 'โครงการอบรม In-House'
      "
      class="course-detail-card"
    >
      <div style="display: flex; align-items: center; margin-bottom: 12px">
        <q-icon name="article" style="margin-right: 8px" />
        <span style="font-weight: bold">ออนไลน์</span>
      </div>
      <div style="font-size: 18px; font-weight: bold; margin-bottom: 4px">
        การใช้งานระบบสารสนเทศของมหาวิทยาลัย
      </div>
      <div style="color: #757575; margin-bottom: 8px">
        ทักษะ : การใช้งานระบบสารสนเทศของมหาวิทยาลัย
      </div>
      <div style="color: #757575; font-size: 13px; margin-bottom: 18px">
        22 พฤษภาคม 2568, 13:30-14:30 น.
      </div>
      <q-btn
        color="primary"
        text-color="white"
        style="width: 100%; font-weight: bold"
        label="ลงทะเบียน"
      />
    </div>
    <div v-else-if="selectedCourse === 'โครงการอบรม In-House'" class="course-detail-card">
      <div
        style="
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          color: #616161;
          font-size: 15px;
        "
      >
        <q-icon name="meeting_room" style="margin-right: 6px" />
        ห้องประชุม IC-203
      </div>
      <div style="font-size: 18px; font-weight: bold; margin-bottom: 4px; line-height: 1.3">
        ความเข้าใจในสถานภาพของมหาวิทยาลัยและความจำเป็นในการเพิ่มขีดความสามารถในการแข่งขัน
      </div>
      <div style="color: #757575; margin-bottom: 8px; line-height: 1.3">
        ทักษะ : การใช้งานระบบสารสนเทศของมหาวิทยาลัย
      </div>
      <div style="color: #757575; font-size: 13px; margin-bottom: 18px">
        22 พฤษภาคม 2568, 13:30-14:30 น.
      </div>
      <q-btn
        color="primary"
        text-color="white"
        style="width: 100%; font-weight: bold"
        label="ลงทะเบียน"
      />
    </div>
    <div v-else-if="selectedCourse === 'ส่งหลักฐานการอบรมจากภายนอก'" class="course-detail-card">
      <div style="text-align: center; font-size: 28px; font-weight: bold; margin-bottom: 18px">
        อัปโหลดหลักฐาน
      </div>
      <input type="file" ref="fileInput" style="display: none" @change="onFileChange" />
      <div
        style="
          border: 1px dashed #bdbdbd;
          border-radius: 12px;
          padding: 32px 0;
          margin-bottom: 16px;
          text-align: center;
          color: #222;
          cursor: pointer;
        "
        @click="triggerFileInput"
      >
        <div style="margin-bottom: 12px; color: #222">กดเพื่ออัปโหลดไฟล์</div>
        <div
          v-if="selectedFile"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 8px;
          "
        >
          <q-icon name="attach_file" size="18px" />
          <q-chip dense clickable style="font-size: 13px">{{ selectedFile.name }}</q-chip>
        </div>
        <div
          v-else
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 8px;
            opacity: 0.5;
          "
        >
          <q-icon name="attach_file" size="18px" />
          <q-chip dense clickable style="font-size: 13px">ยังไม่มีไฟล์</q-chip>
        </div>
      </div>
      <div style="display: flex; justify-content: center">
        <q-btn
          color="positive"
          icon="cloud_upload"
          label="Upload"
          style="font-weight: bold; min-width: 140px"
          :disable="!selectedFile"
          @click="triggerFileInput"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const courses = ref(['หลักสูตร E-Learning', 'โครงการอบรม In-House', 'ส่งหลักฐานการอบรมจากภายนอก']);
const selectedCourse = ref<string | null>(null);

function selectCourse(course: string) {
  selectedCourse.value = course;
}
function closeDetail() {
  selectedCourse.value = null;
}

// --- เพิ่มสำหรับอัปโหลดไฟล์ ---
const selectedFile = ref<File | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);

function triggerFileInput() {
  fileInput.value?.click();
}
function onFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0] ?? null;
  } else {
    selectedFile.value = null;
  }
}
</script>

<style scoped>
.title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 14px;
}
.chip-list {
  display: flex;
  gap: 12px;
  flex-wrap: nowrap;
  overflow-x: auto;
}
.chip {
  display: flex;
  align-items: center;
  border: 1px solid #dddddd;
  border-radius: 12px;
  padding: 4px 10px 6px 4px;
  font-size: 12px;
  color: #9e9e9e;
  background: #fff;
  transition:
    border-color 0.2s,
    color 0.2s,
    background 0.2s;
  cursor: pointer;
}
.chip.active {
  border-color: var(--q-primary);
  background: var(--q-primary);
  color: white;
}

.close {
  margin-left: 8px;
  font-size: 1.2em;
  opacity: 0.6;
  cursor: pointer;
  user-select: none;
}
.chip-label {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
}
.course-detail-card {
  position: relative;
  border: 1px solid #dddddd;
  border-radius: 20px;
  padding: 24px;
  background: #fff;
  min-height: 180px;
  margin-top: 18px;
}
</style>
