<template>
  <div class="q-px-lg q-py-md">
    <q-timeline color="secondary" layout="loose" class="custom-timeline">
      <q-timeline-entry
        v-for="(period, idx) in periods"
        :key="period.label"
        :title="period.label"
        icon="fiber_manual_record"
        :color="period.color"
        :side="idx % 2 === 0 ? 'left' : 'right'"
        class="custom-timeline-entry"
      >
        <q-btn
          flat
          round
          dense
          size="sm"
          :icon="expanded[idx] ? 'expand_less' : 'expand_more'"
          class="q-mb-sm"
          @click="toggleDetail(idx)"
        />

        <div v-show="expanded[idx]" class="timeline-detail">
          <div v-for="(line, lidx) in period.details" :key="lidx" class="timeline-detail-line">
            {{ line }}
          </div>
        </div>
      </q-timeline-entry>
    </q-timeline>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const periods = [
  {
    label: 'อายุการปฏิบัติงาน 1-2 ปี',
    color: 'positive',
    details: [
      'ความรู้และทักษะทั่วไป',
      'การทำงาน ภายใต้ความ และการปฏิบัติงานที่ได้รับมอบหมายจากหัวหน้า',
      'การเขียนบันทึกผลลัพธ์ทางวิชาการ',
      'การพัฒนาทักษะการคิดวิเคราะห์',
      'การทำงานเป็นทีม',
      'การบริหารเวลาและวินัยของตนเอง',
      'กิจกรรมเสริมสร้างทักษะที่เกี่ยวข้อง',
      'การพัฒนาตนเองอย่างต่อเนื่อง',
      'ความตั้งใจในผลงานตามภาระงาน/ภาระหน้าที่และความคาดหวังของหน่วยงาน',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารจัดการห้องสมุด ภาษาอังกฤษ การสอน และประเมินผล',
      'การพัฒนาโครงการวิจัยและบทความทางวิชาการ',
      'การนำเสนอผลงานทางวิชาการ',
      'การใช้โปรแกรมระบบสารสนเทศทางวิชาชีพ',
      'การพัฒนาระบบงานตามมาตรฐานสากล',
    ],
  },
  {
    label: 'อายุการปฏิบัติงาน 3-5 ปี',
    color: 'primary',
    details: [
      'ความรู้และทักษะทั่วไป',
      'การทำงานแบบมืออาชีพ',
      'การสร้างนวัตกรรมในการปฏิบัติงาน',
      'การแก้ไขปัญหาเชิงสร้างสรรค์',
      'กิจกรรมเสริมสร้างทักษะองค์กร (ต่อเนื่อง)',
      'การพัฒนาตนเองอย่างต่อเนื่อง (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารจัดการห้องสมุด ภาษาอังกฤษ การสอน และประเมินผล (ต่อเนื่อง)',
      'การพัฒนาโครงการวิจัยและบทความทางวิชาการ (ต่อเนื่อง)',
      'การนำเสนอผลงานทางวิชาการ (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐานสากล (ต่อเนื่อง)',
    ],
  },
  {
    label: 'อายุการปฏิบัติงาน 6-8 ปี',
    color: 'grey-5',
    details: [
      'ทักษะทั่วไป',
      'การทำงานแบบมืออาชีพ (ต่อเนื่อง)',
      'การแก้ไขปัญหาเชิงสร้างสรรค์ (ต่อเนื่อง)',
      'หลักการทางวิชาการ',
      'กิจกรรมเสริมสร้างทักษะองค์กร (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารโครงการวิจัยและบทความทางวิชาการ (ต่อเนื่อง)',
      'การนำเสนอผลงานทางวิชาการ (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
    ],
  },
  {
    label: 'อายุการปฏิบัติงาน 9 ปีขึ้นไป',
    color: 'grey-5',
    details: [
      'ความรู้และทักษะทั่วไป',
      'หลักการทางวิชาการ (ต่อเนื่อง)',
      'กิจกรรมเสริมสร้างทักษะองค์กร (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
      'ความรู้และทักษะเฉพาะด้าน (ประเภทวิชาการ)',
      'การบริหารโครงการวิจัยและบทความทางวิชาการ (ต่อเนื่อง)',
      'การนำเสนอผลงานทางวิชาการ (ต่อเนื่อง)',
      'การพัฒนาระบบงานตามมาตรฐาน (ต่อเนื่อง)',
    ],
  },
];

const expanded = ref(periods.map(() => true));

function toggleDetail(index: number) {
  expanded.value[index] = !expanded.value[index];
}
</script>

<style scoped>
.custom-timeline {
  max-width: 900px;
  margin: 0 auto;
}
.custom-timeline-entry {
  font-size: 1rem;
}
.timeline-detail {
  background: #fafafa;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 8px;
  margin-top: 8px;
}
.timeline-detail-line {
  margin-bottom: 4px;
}
</style>
