<script setup lang="ts">
import { ref, watch } from 'vue';

interface TabItem {
  label: string;
  value: string;
}

const props = defineProps<{
  tabs: TabItem[];
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', val: string): void;
}>();

const tab = ref(props.modelValue);

watch(tab, (val) => emit('update:modelValue', val));
</script>

<template>
  <q-tabs v-model="tab" dense class="text-grey-8 bg-white" align="left">
    <q-tab
      v-for="item in tabs"
      :key="item.value"
      :name="item.value"
      :label="item.label"
      class="q-px-md"
    />
  </q-tabs>
</template>
<style scoped>
::v-deep(.q-tab--active) {
  background-color: #f0f0f0;
  border-radius: 4px;
  transition: background-color 0.2s;
}
</style>
