<template>
  <div :class="['status-capsule', statusClass]">
    {{ statusText }}
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';

type StatusType = string;

const props = defineProps<{
  status?: StatusType;
  published?: boolean;
}>();

// Backward compatibility: if 'status' is not provided, use 'published' boolean
const effectiveStatus = computed<StatusType>(() => {
  if (props.status) return props.status;
  if (typeof props.published === 'boolean') {
    return props.published ? 'published' : 'unpublished';
  }
  return 'unpublished';
});

const statusMap: Record<string, { text: string; class: string }> = {
  published: { text: 'เผยแพร่แล้ว', class: 'published' },
  unpublished: { text: 'ยังไม่เผยแพร่', class: 'unpublished' },
  // สามารถเพิ่มสถานะใหม่ได้ที่นี่ เช่น
  // draft: { text: 'ฉบับร่าง', class: 'draft' },
};

const statusText = computed(() => statusMap[effectiveStatus.value]?.text || effectiveStatus.value);
const statusClass = computed(() => statusMap[effectiveStatus.value]?.class || 'other-status');
</script>

<style scoped>
.status-capsule {
  display: inline-block;
  padding: 3px 16px;
  border-radius: 24px;
  font-size: 16px;
  font-family: inherit;
  font-weight: 400;
  border: 1px solid;
  text-align: center;
  margin: 4px 0;
  transition:
    background 0.2s,
    color 0.2s,
    border 0.2s;
}
.published {
  background: #e3f6e8;
  color: #389e4a;
  border-color: #389e4a;
}
.unpublished {
  background: #f3e6e6;
  color: #a13a3a;
  border-color: #a13a3a;
}
.other-status {
  background: #e0e0e0;
  color: #555;
  border-color: #bbb;
}
</style>
