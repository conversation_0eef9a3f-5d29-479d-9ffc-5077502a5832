<template>
  <q-dialog v-model="dialog" persistent>
    <q-card class="q-pa-lg q-mx-sm" style="border-radius: 16px; width: 100%; max-width: 400px">
      <q-card-section class="text-h6 text-center">เปลี่ยนสถานะการเผยแพร่</q-card-section>

      <!-- Card สถานะ 2 ฝั่ง -->
      <q-card-section>
        <div :class="$q.screen.lt.sm ? 'column q-gutter-sm' : 'row justify-around'">
          <!-- FALSE -->
          <div :class="$q.screen.lt.sm ? '' : 'col-5'">
            <q-card
              flat
              bordered
              class="text-center cursor-pointer"
              :class="!assStastatus ? 'bg-secondary text-white' : ''"
              @click="assStastatus = false"
            >
              <q-card-section>
                <q-icon name="cancel" size="24px" />
                <div class="text-subtitle1 q-mt-sm">ไม่เผยแพร่</div>
              </q-card-section>
            </q-card>
          </div>

          <!-- TRUE -->
          <div :class="$q.screen.lt.sm ? '' : 'col-5'">
            <q-card
              flat
              bordered
              class="text-center"
              :class="[assStastatus ? 'bg-positive text-white' : '']"
              @click="onPublishClick"
            >
              <q-card-section>
                <q-icon name="check_circle" size="24px" />
                <div class="text-subtitle1 q-mt-sm">เผยแพร่อยู่</div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- Validation Message -->
        <div v-if="showValidationMessage" class="q-mt-md">
          <q-banner class="bg-orange-1 text-orange-8" rounded>
            <template v-slot:avatar>
              <q-icon name="warning" color="orange" />
            </template>
            กรุณากำหนดวันที่เปิดและปิดการประเมินก่อนเผยแพร่
          </q-banner>
        </div>
      </q-card-section>

      <!-- ปุ่ม -->
      <q-card-actions
        :class="$q.screen.lt.sm ? 'column items-stretch' : 'row justify-end'"
        class="q-gutter-sm"
      >
        <q-btn
          label="ยกเลิก"
          color="grey-4"
          class="text-black"
          @click="dialog = false"
          :class="$q.screen.lt.sm ? 'full-width' : ''"
        />
        <q-btn
          label="ยืนยัน"
          color="primary"
          class="text-black"
          @click="confirm"
          :class="$q.screen.lt.sm ? 'full-width' : ''"
          :disable="assStastatus && showValidationMessage"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import type { AssessmentType } from 'src/types/data';

const $q = useQuasar();

const dialog = ref(false);
const assId = ref(0);
const assStastatus = ref(false);
const confirmLabel = ref('ยืนยัน');
const path = ref<AssessmentType>('evaluate');
const assessmentStartAt = ref<string | null>(null);
const assessmentEndAt = ref<string | null>(null);
const showValidationMessage = ref(false);

// Computed property to check if assessment can be published
const canPublish = computed(() => {
  return assessmentStartAt.value && assessmentEndAt.value;
});

function openDialog(
  id: number,
  status: boolean,
  okStr: string,
  pathPrefix: string,
  startAt?: string | null,
  endAt?: string | null,
) {
  assId.value = id;
  assStastatus.value = status;
  confirmLabel.value = okStr;
  path.value = pathPrefix as AssessmentType;
  assessmentStartAt.value = startAt || null;
  assessmentEndAt.value = endAt || null;
  showValidationMessage.value = false; // Reset validation message
  dialog.value = true;
}

function onPublishClick() {
  assStastatus.value = true;
  if (!canPublish.value) {
    // Show validation message if trying to publish without required dates
    showValidationMessage.value = true;
    return;
  }
  // Hide validation message if dates are set
  showValidationMessage.value = false;
}

async function confirm() {
  // Prevent confirmation if trying to publish without required dates
  if (assStastatus.value && !canPublish.value) {
    showValidationMessage.value = true;
    return;
  }

  await new AssessmentService(path.value).updateOne(assId.value, { status: assStastatus.value });
  dialog.value = false;
  window.location.reload();
}

defineExpose({ openDialog });
</script>
