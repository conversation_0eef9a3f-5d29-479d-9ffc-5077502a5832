<template>
  <div class="row justify-between items-start">
    <div class="col-12">
      <q-card class="col-auto my-card">
        <q-card-section class="row items-center q-gutter-x-lg">
          <q-img id="avatar-img" src="https://cdn.quasar.dev/img/avatar4.jpg" alt="Avatar" />
          <div class="col q-gutter-y-md">
            <div class="text-h6">ผศ. ลลิตา มะลิวัน</div>
            <div class="">ชื่อ: ลลิตา มะลิวัน</div>
            <div class="">อายุปฏิบัติงาน: 4 ปี</div>
            <div class="">ตำแหน่ง: อาจารย์/ผู้ช่วยศาสตราจารย์</div>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
#avatar-img {
  width: 150px;
  height: 150px;
  border-radius: $generic-border-radius / 2;
}

.my-card {
  width: 100%;
  min-width: 480px;
  max-width: 550px;
}
</style>
