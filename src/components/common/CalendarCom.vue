<template>
  <div class="calendar-border">
    <div class="calendar-container">
      <div class="calendar-header q-pa-md row items-center" style="background: #fff">
        <div class="calendar-title text-h6 text-weight-bold row items-center">
          {{ monthOptions[selectedMonth]?.label || '' }}
          <q-btn-dropdown flat dense class="q-ml-sm" :label="selectedYear" style="min-width: 80px">
            <q-list style="min-width: 100px">
              <q-item
                v-for="year in yearOptions"
                :key="year"
                clickable
                v-close-popup
                @click="setYear(year)"
              >
                <q-item-section>{{ year }}</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </div>
        <div class="q-gutter-x-sm q-ml-auto">
          <q-btn flat round icon="chevron_left" @click="prevMonth" />
          <q-btn flat round icon="chevron_right" @click="nextMonth" />
        </div>
      </div>
      <q-calendar-month
        v-model:model-value="selectedDate"
        :day-min-height="90"
        :events="events"
        :event-color="getEventColor"
        :weekday-labels="['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส']"
        locale="th-TH"
        animated
        class="calendar-main"
      >
        <template #day="{ scope }">
          <div class="q-pa-xs column items-center justify-center full-height">
            <div class="text-subtitle2">{{ scope.day }}</div>
            <template v-if="getEventsForDay(scope.timestamp.date).length === 1">
              <div
                v-for="event in getEventsForDay(scope.timestamp.date)"
                :key="event.id"
                :style="{ marginBottom: '1px' }"
              >
                <q-chip
                  dense
                  color="grey-5"
                  text-color="black"
                  class="full-width justify-center"
                  style="max-width: 100%; font-size: 12px"
                >
                  {{ event.name }}
                </q-chip>
              </div>
            </template>
            <template v-else-if="getEventsForDay(scope.timestamp.date).length === 2">
              <div
                v-for="event in getEventsForDay(scope.timestamp.date)"
                :key="event.id"
                :style="{ marginBottom: '1px' }"
              >
                <q-chip
                  dense
                  color="grey-5"
                  text-color="black"
                  class="full-width justify-center"
                  style="max-width: 100%; font-size: 12px"
                >
                  {{ event.name }}
                </q-chip>
              </div>
            </template>
            <template v-else-if="getEventsForDay(scope.timestamp.date).length > 2">
              <div :style="{ marginBottom: '1px', width: '100%', textAlign: 'center' }">
                <q-chip
                  dense
                  color="primary"
                  text-color="black"
                  class="full-width justify-center"
                  style="max-width: 100%; font-size: 12px"
                >
                  • {{ getEventsForDay(scope.timestamp.date).length }} กิจกรรม
                </q-chip>
              </div>
            </template>
          </div>
        </template>
      </q-calendar-month>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const buddhistToGregorian = (year: number) => year - 543;

const today = new Date();
const initialYear = today.getFullYear() + 543;
const initialMonth = today.getMonth();

const selectedYear = ref(initialYear);
const selectedMonth = ref(initialMonth);

const monthOptions = [
  { label: 'มกราคม', value: 0 },
  { label: 'กุมภาพันธ์', value: 1 },
  { label: 'มีนาคม', value: 2 },
  { label: 'เมษายน', value: 3 },
  { label: 'พฤษภาคม', value: 4 },
  { label: 'มิถุนายน', value: 5 },
  { label: 'กรกฎาคม', value: 6 },
  { label: 'สิงหาคม', value: 7 },
  { label: 'กันยายน', value: 8 },
  { label: 'ตุลาคม', value: 9 },
  { label: 'พฤศจิกายน', value: 10 },
  { label: 'ธันวาคม', value: 11 },
];

const selectedDate = ref(
  `${buddhistToGregorian(selectedYear.value)}-${String(selectedMonth.value + 1).padStart(2, '0')}-01`,
);

watch([selectedYear, selectedMonth], () => {
  selectedDate.value = `${buddhistToGregorian(selectedYear.value)}-${String(selectedMonth.value + 1).padStart(2, '0')}-01`;
});

function prevMonth() {
  if (selectedMonth.value === 0) {
    selectedMonth.value = 11;
    selectedYear.value--;
  } else {
    selectedMonth.value--;
  }
}
function nextMonth() {
  if (selectedMonth.value === 11) {
    selectedMonth.value = 0;
    selectedYear.value++;
  } else {
    selectedMonth.value++;
  }
}

const yearOptions = Array.from({ length: 11 }, (_, i) => initialYear - 5 + i);

function setYear(year: number) {
  selectedYear.value = year;
}

interface CalendarEvent {
  id: number;
  name: string;
  start: string;
  end: string;
  color?: string;
}

const events: CalendarEvent[] = [
  {
    id: 1,
    name: 'โครงการสานสัมพันธ์บุคลากรใหม่',
    start: '2025-06-12',
    end: '2025-06-12',
  },
  {
    id: 2,
    name: 'ประชุมทีม',
    start: '2025-06-12',
    end: '2025-06-12',
  },
  {
    id: 3,
    name: 'อบรมความปลอดภัย',
    start: '2025-06-12',
    end: '2025-06-12',
  },
  {
    id: 4,
    name: 'กิจกรรมกีฬา',
    start: '2025-06-12',
    end: '2025-06-12',
  },
  {
    id: 5,
    name: 'สัมมนาวิชาการ',
    start: '2025-06-13',
    end: '2025-06-13',
  },
  {
    id: 6,
    name: 'กิจกรรมอาสา',
    start: '2025-06-13',
    end: '2025-06-13',
  },
  {
    id: 7,
    name: 'งานเลี้ยงบริษัท',
    start: '2025-06-14',
    end: '2025-06-14',
  },
  {
    id: 8,
    name: 'ตรวจสุขภาพ',
    start: '2025-06-14',
    end: '2025-06-14',
  },
  {
    id: 9,
    name: 'กิจกรรม CSR',
    start: '2025-06-14',
    end: '2025-06-14',
  },
  {
    id: 10,
    name: 'โครงการอบรมภายใน',
    start: '2025-07-12',
    end: '2025-07-12',
  },
  {
    id: 11,
    name: 'กิจกรรมสัมพันธ์',
    start: '2025-07-12',
    end: '2025-07-12',
  },
  {
    id: 12,
    name: 'กิจกรรมพิเศษ',
    start: '2025-07-12',
    end: '2025-07-12',
  },
];

function getEventColor() {
  return 'grey';
}

function getEventsForDay(date: string): CalendarEvent[] {
  return events.filter((event) => {
    return date >= event.start && date <= event.end;
  });
}
</script>

<style scoped>
.calendar-border {
  border: 2px solid #d9d9d9;
  border-radius: 12px;
  padding: 8px;
  background: #fff;
  width: fit-content;
}

.calendar-container {
  display: flex;
  flex-direction: column;
  width: 800px;
  height: 500px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  background: #fff;
  border-bottom: 1px solid #f5d76e;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
}

.calendar-title {
  min-width: 180px;
  text-align: left;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}

.calendar-main {
  flex: 1;
  background: #fff;
}

:deep(.q-calendar-month__head) {
  background: var(--q-primary);
  height: 50px;
  min-height: 50px;
  max-height: 50px;
  border-bottom: 1px solid #ccc;
  color: black !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.q-calendar-month__head .q-calendar__head--weekday) {
  color: black !important;
  font-weight: 600;
  text-align: center;
  font-size: 16px;
}

:deep(.q-calendar-month__day) {
  border: 1px solid #e0e0e0;
  height: 90px;
  min-height: 90px;
  max-height: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: black !important;
  font-size: 13px;
  position: relative;
  text-align: center;
  padding: 4px;
}

:deep(.q-calendar-month__day--content) {
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

:deep(.q-calendar-month__event) {
  display: none;
}
</style>
