<template>
  <q-drawer
    :mini="miniState"
    @mouseenter="miniState = false"
    @mouseleave="miniState = true"
    :breakpoint="500"
    show-if-above
    v-model:model-value="globalStore.leftDrawerState"
    side="left"
    bordered
    :class="{ 'drawer-mini': miniState }"
  >
    <div class="column full-height q-pt-sm">
      <q-list>
        <q-item
          v-for="item in menu"
          :key="item.title"
          clickable
          :to="item.link"
          exact
          active-class="bg-primary text-black"
          class="menu-item"
        >
          <q-item-section avatar>
            <q-icon :name="item.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-subtitle1">{{ item.title }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>

      <q-space />

      <q-separator />

      <q-list>
        <q-item v-if="logOutMenu?.[0]" clickable @click="handleLogoutClick" class="menu-item">
          <q-item-section avatar>
            <q-icon color="red" :name="logOutMenu?.[0]?.icon" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-red text-body1">{{ logOutMenu?.[0]?.title }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
  </q-drawer>
</template>

<script setup lang="ts">
import { useGlobalStore } from 'src/stores/global';
import type { MenuLink } from 'src/types/app';
import { ref } from 'vue';
import { LogOutMenu } from 'src/data/menu';

const globalStore = useGlobalStore();
const miniState = ref(true);
const emit = defineEmits<(e: 'logout') => void>();

const handleLogoutClick = () => {
  emit('logout');
};

defineProps<{
  menu: MenuLink[];
}>();

const logOutMenu = LogOutMenu;
</script>

<style scoped lang="scss">
.menu-item {
  border-radius: 0 $generic-border-radius $generic-border-radius 0;
}

.drawer-mini .menu-item {
  border-radius: 0px;
}
</style>
