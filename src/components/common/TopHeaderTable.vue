<template>
  <div class="top-header-table">
    <!-- แถวหลัก แบ่งซ้าย-ขวา -->
    <div class="row q-mt-sm q-mb-sm items-center justify-between">
      <!-- :white_check_mark: ฝั่งซ้าย: Title + Tab + Subtitle -->
      <div class="col-auto">
        <div class="text-h6">{{ title }}</div>
        <slot name="tab" />
        <div v-if="subtitle" class="text-subtitle1 text-weight-medium q-mt-lg">
          {{ subtitle }}
        </div>
      </div>

      <!-- :white_check_mark: ฝั่งขวา: Search + ปุ่มเพิ่ม -->
      <div class="items-center q-gutter-sm">
        <div class="q-mt-xl flex justify-end">
          <q-btn
            v-if="showCreateButton"
            :label="createButtonLabel"
            color="accent"
            icon="add"
            @click="handleCreate"
          />
        </div>
        <div class="row items-center justify-between" style="margin-top: 25px">
          <!-- ซ้าย: SearchBar -->
          <div class="col">
            <SearchBar v-if="showSearch" @search="handleSearch" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <CreateSkillDialog
    v-if="skillStore.createDialog"
    v-model="skillStore.createDialog"
    :skill-type="skillStore.selectedTap"
    :title="skillStore.dialogTitile"
  />
</template>

<script setup lang="ts">
import CreateSkillDialog from 'src/components/skill/createSkillDialog.vue';
import SearchBar from 'src/components/SearchBar.vue';
import { useSkillStore } from 'src/stores/skills';
import { onMounted } from 'vue';

interface Props {
  title: string;
  createButtonLabel?: string;
  showCreateButton?: boolean;
  showSearch?: boolean;
  subtitle?: string;
}
const skillStore = useSkillStore();

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
}

const props = withDefaults(defineProps<Props>(), {
  createButtonLabel: 'สร้าง',
  showCreateButton: true,
  showSearch: true,
});
onMounted(() => {
  console.log(props.title);
});
const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleCreate = () => {
  skillStore.dialogTitile = 'สร้างความรู้และทักษะ';
  skillStore.createDialog = true;
};
</script>
