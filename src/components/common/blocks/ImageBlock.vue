<script setup lang="ts">
import EditorTool from 'src/components/common/EditorTool.vue';
import { computed, ref, watch, nextTick } from 'vue';
import type { ImageBody, ItemBlock } from 'src/types/models';
import type { CSSProperties } from 'vue';
import ItemBlockFooter from './ItemBlockFooter.vue';
import FloatImageBtn from '../FloatImageBtn.vue';
import { useGlobalStore } from 'src/stores/global';
import { ImageBodyService } from 'src/services/asm/imageBodyService';
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

const emit = defineEmits(['focus-fab', 'duplicate', 'delete', 'update:image']);

// UI state management
const showMenu = ref(false);

// Initialize showImageText based on existing imageText data
// If there's existing imageText content, the toggle should be enabled
const initialImageTextExists =
  props.itemBlock.imageBody?.imageText && props.itemBlock.imageBody.imageText.trim() !== '';
const showImageText = ref(initialImageTextExists); // Toggle state for imageText field

const globalStore = useGlobalStore();
const selectedFile = ref<File | null>(null);
const itemBlockId = ref(props.itemBlock.id || 0);
const imageId = ref(props.itemBlock.imageBody?.id || 0);

// Store original image path to preserve relative path format
const originalImagePath = ref<string | null>(null);

// Utility function to detect if a path is a relative path (not a signed URL)
const isRelativePath = (path: string | null | undefined): boolean => {
  if (!path) return false;
  // Relative paths start with 'uploaded_files/' and don't contain query parameters or full URLs
  return path.startsWith('uploaded_files/') && !path.includes('?') && !path.includes('http');
};

// Utility function to extract relative path from signed URL
const extractRelativePath = (path: string | null | undefined): string | null => {
  if (!path) return null;

  // If it's already a relative path, return as is
  if (isRelativePath(path)) return path;

  // If it's a signed URL, extract the relative path part
  if (path.includes('uploaded_files/')) {
    const match = path.match(/uploaded_files\/[^?]+/);
    return match ? match[0] : null;
  }

  return path;
};

// Initialize imageText - treat empty string and null as undefined to show placeholder
const initialImageText = props.itemBlock.imageBody?.imageText;
const imageText = ref(
  initialImageText === '' || initialImageText === null ? undefined : initialImageText,
);
const lastSavedImageText = ref(imageText.value);

// Initialize original image path - preserve relative path format
const initImagePath = props.itemBlock.imageBody?.imagePath;
if (initImagePath && isRelativePath(initImagePath)) {
  originalImagePath.value = initImagePath;
} else if (initImagePath) {
  // Extract relative path from signed URL
  const relativePath = extractRelativePath(initImagePath);
  if (relativePath) {
    originalImagePath.value = relativePath;
  }
}

// Watch for changes in itemBlock props to update local state
watch(
  () => props.itemBlock.imageBody?.imageText,
  (newImageText) => {
    // Treat empty string and null as undefined to show placeholder
    const processedText = newImageText === '' || newImageText === null ? undefined : newImageText;
    if (processedText !== imageText.value) {
      imageText.value = processedText;
      lastSavedImageText.value = processedText;
    }

    // CRITICAL: Update showImageText toggle based on whether there's actual text content
    // This ensures the toggle state persists across page refreshes and navigation
    const hasTextContent = newImageText && newImageText.trim() !== '';
    if (showImageText.value !== hasTextContent) {
      showImageText.value = hasTextContent;
    }
  },
  { immediate: true },
);

// Watch for changes in imageBody ID to update local state
watch(
  () => props.itemBlock.imageBody?.id,
  (newImageId) => {
    if (newImageId !== undefined) {
      imageId.value = newImageId;
    }
  },
  { immediate: true },
);

// Watch for changes in imagePath to preserve original relative path format
watch(
  () => props.itemBlock.imageBody?.imagePath,
  (newImagePath) => {
    if (newImagePath) {
      // If we don't have an original path stored yet, or if this is a new relative path
      if (!originalImagePath.value || isRelativePath(newImagePath)) {
        if (isRelativePath(newImagePath)) {
          originalImagePath.value = newImagePath;
        } else {
          // Try to extract relative path from signed URL
          const relativePath = extractRelativePath(newImagePath);
          if (relativePath && !originalImagePath.value) {
            originalImagePath.value = relativePath;
          }
        }
      }
    }
  },
  { immediate: true },
);

// Watch for changes in itemBlock ID to update local state
watch(
  () => props.itemBlock.id,
  (newItemBlockId) => {
    if (newItemBlockId !== undefined) {
      itemBlockId.value = newItemBlockId;
    }
  },
  { immediate: true },
);

// Watch for changes in image dimensions to trigger container adaptation
watch(
  () => [props.itemBlock.imageBody?.imageWidth, props.itemBlock.imageBody?.imageHeight],
  ([newWidth, newHeight], [oldWidth, oldHeight]) => {
    if (newWidth !== oldWidth || newHeight !== oldHeight) {
      // The containerStyle computed property will automatically recalculate
      // due to its reactive dependencies on imageWidth and imageHeight
    }
  },
  { immediate: false },
);

const imgUrl = computed(() => {
  const imagePath = props.itemBlock.imageBody?.imagePath || '';
  return imagePath;
});

const imageStyle = computed<CSSProperties>(() => {
  const body = props.itemBlock.imageBody;

  // Use effective dimensions (local overrides or props)
  const currentWidth = effectiveImageWidth.value;
  const currentHeight = effectiveImageHeight.value;

  // If no dimensions are available, use auto sizing with reasonable constraints
  if (!body || !currentWidth || !currentHeight || currentWidth <= 0 || currentHeight <= 0) {
    return {
      width: 'auto',
      height: 'auto',
      objectFit: 'contain',
      display: 'block', // optional: removes inline whitespace
    };
  }

  const originalWidth = currentWidth;
  const originalHeight = currentHeight;

  // Define maximum container constraints for responsive design
  const maxContainerWidth = 1200; // Maximum width for very large images
  const maxContainerHeight = 800; // Maximum height for very tall images
  const minDisplayWidth = 200; // Minimum width for very small images
  const minDisplayHeight = 150; // Minimum height for very small images

  let finalWidth = originalWidth;
  let finalHeight = originalHeight;

  // Handle very large images - scale down proportionally
  if (originalWidth > maxContainerWidth || originalHeight > maxContainerHeight) {
    const widthScale = maxContainerWidth / originalWidth;
    const heightScale = maxContainerHeight / originalHeight;
    const scale = Math.min(widthScale, heightScale);

    finalWidth = Math.round(originalWidth * scale);
    finalHeight = Math.round(originalHeight * scale);
  }
  // Handle very small images - ensure minimum usable size
  else if (originalWidth < minDisplayWidth || originalHeight < minDisplayHeight) {
    const widthScale = minDisplayWidth / originalWidth;
    const heightScale = minDisplayHeight / originalHeight;
    const scale = Math.max(widthScale, heightScale);

    // Only scale up if the image is significantly smaller than minimum
    if (scale > 1.5) {
      finalWidth = Math.round(originalWidth * scale);
      finalHeight = Math.round(originalHeight * scale);
    }
  }

  return {
    width: `${finalWidth}px`,
    height: `${finalHeight}px`,
    objectFit: 'contain' as const,
    display: 'block', // ทำให้รูป block-level เพื่อไม่ให้มีช่องว่าง inline
    margin: '20px', // ลบ margin รอบภาพ
    maxWidth: 'none', // ไม่ให้ขยายตาม container
    maxHeight: 'none',
  };
});

// Simplified container style that adapts naturally to content
const containerStyle = computed<CSSProperties>(() => {
  return {
    cursor: 'pointer',
    minHeight: '200px', // Minimal height for empty state
    transition: 'box-shadow 0.2s ease',
  };
});

async function performSaveImageText() {
  const imageBody = props.itemBlock.imageBody;
  const isCreate = !imageBody?.id;

  // Convert undefined to empty string for comparison
  const currentText = imageText.value || '';
  const lastSavedText = lastSavedImageText.value || '';

  if (currentText.trim() === lastSavedText.trim() && !selectedFile.value) {
    return;
  }

  try {
    globalStore.startSaveOperation(isCreate ? 'Creating...' : 'Saving...');

    const service = new ImageBodyService();
    let updated: ImageBody;

    if (isCreate) {
      updated = await service.createImageBody(
        {
          itemBlockId: props.itemBlock.id,
          imageText: currentText, // Use converted text value
          id: itemBlockId.value,
        },
        selectedFile.value!,
      );
    } else {
      // Check if we're updating with a file or just text
      if (selectedFile.value) {
        // Update with new file
        updated = await service.updateImageBody(
          imageBody.id,
          {
            itemBlockId: props.itemBlock.id,
            imageText: currentText, // Use converted text value
            id: imageId.value,
          },
          selectedFile.value,
        );
      } else {
        // Text-only update to preserve existing image
        // CRITICAL: Use the original relative path instead of the potentially signed URL from imageBody.imagePath
        const pathToUse = originalImagePath.value || imageBody.imagePath;

        updated = await service.updateImageTextOnly(
          imageBody.id,
          currentText,
          pathToUse || undefined,
          imageBody.imageWidth,
          imageBody.imageHeight,
        );
      }
    }

    // Handle backend response properly
    const backendImageText = updated.imageText;
    if (backendImageText !== undefined && backendImageText !== null && backendImageText !== '') {
      // Backend returned actual text content
      imageText.value = backendImageText;
      lastSavedImageText.value = backendImageText;
    } else {
      // Backend returned null/undefined/empty, treat as undefined for UI to show placeholder
      imageText.value = undefined;
      lastSavedImageText.value = undefined;
    }
    selectedFile.value = null; // เคลียร์ไฟล์หลังบันทึกเสร็จ

    globalStore.completeSaveOperation(
      true,
      isCreate ? 'Created successfully' : 'Saved successfully',
    );

    // if (isCreate) {
    //   emit('update:image', updated);
    // }
  } catch {
    globalStore.completeSaveOperation(false, isCreate ? 'Create failed' : 'Save failed');
  }
}

// Handle click on ImageBlock to focus FAB
const handleImageBlockClick = (event: Event) => {
  // Prevent focusing FAB if clicking on interactive elements
  const target = event.target as HTMLElement;

  // Check if the click is on an interactive element that should not trigger FAB focus
  if (
    target.closest('.q-btn') || // Any button
    target.closest('.q-input') || // Any input field
    target.closest('.q-editor') || // Editor tool
    target.closest('.pixel-image-position') || // FloatImageBtn
    target.closest('[contenteditable]') // Any contenteditable element
  ) {
    return;
  }

  // Emit focus-fab event to position the FAB on this ImageBlock
  emit('focus-fab', props.itemBlock.id);
};

// Reactive local dimensions that override props when updated
const localImageWidth = ref<number | null>(null);
const localImageHeight = ref<number | null>(null);

// Computed properties that use local dimensions if available, otherwise fall back to props
const effectiveImageWidth = computed(() => {
  return localImageWidth.value ?? props.itemBlock.imageBody?.imageWidth ?? 0;
});

const effectiveImageHeight = computed(() => {
  return localImageHeight.value ?? props.itemBlock.imageBody?.imageHeight ?? 0;
});

// Watch for prop changes and reset local dimensions when backend data is updated
watch(
  () => [props.itemBlock.imageBody?.imageWidth, props.itemBlock.imageBody?.imageHeight],
  ([newWidth, newHeight], [oldWidth, oldHeight]) => {
    // Only reset local dimensions if the props actually changed from backend
    // and we don't have pending local changes
    if (
      (newWidth !== oldWidth || newHeight !== oldHeight) &&
      newWidth !== localImageWidth.value &&
      newHeight !== localImageHeight.value
    ) {
      // Reset local overrides to sync with backend
      localImageWidth.value = null;
      localImageHeight.value = null;
    }
  },
  { deep: true },
);

// Handle dimensions updated from FloatImageBtn
const handleDimensionsUpdated = async (dimensions: { width: number; height: number }) => {
  // CRITICAL: Update local reactive dimensions immediately to prevent image disappearance
  localImageWidth.value = dimensions.width;
  localImageHeight.value = dimensions.height;

  // Emit to parent component for store updates
  emit('update:image', {
    itemBlockId: props.itemBlock.id,
    dimensions: dimensions,
  });

  // CRITICAL: Ensure FAB remains positioned on this ImageBlock after dimension update
  // Wait for DOM updates to complete before repositioning FAB
  await nextTick();
  await nextTick();

  // Explicitly emit focus-fab to ensure FAB stays with this ImageBlock
  emit('focus-fab', props.itemBlock.id);
};

// Menu handlers
const toggleMenu = () => {
  showMenu.value = !showMenu.value;
};

const onDuplicate = () => {
  emit('duplicate');
};

const onDelete = () => {
  emit('delete');
};

// Toggle imageText field visibility
const toggleImageText = (value: boolean) => {
  showImageText.value = value;
};
</script>

<template>
  <q-card
    class="q-pa-lg image-block-container"
    :style="containerStyle"
    @click="handleImageBlockClick"
  >
    <!-- Header with three-dot menu -->
    <div class="image-top-bar">
      <div class="row items-center q-gutter-sm">
        <!-- Three-dot menu -->
        <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
          <q-icon name="more_horiz" size="30px"></q-icon>
          <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
            <q-list style="min-width: 150px">
              <q-item clickable v-close-popup @click="onDuplicate">
                <q-item-section avatar>
                  <q-icon name="content_copy" />
                </q-item-section>
                <q-item-section>Duplicate</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="onDelete">
                <q-item-section avatar>
                  <q-icon name="delete" />
                </q-item-section>
                <q-item-section>Delete</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
    </div>

    <!-- Content area that grows to fill available space -->
    <div class="image-block-content">
      <!-- Conditional imageText field -->
      <EditorTool
        v-if="showImageText"
        class="q-mt-sm"
        label="ข้อความ..."
        :initialValue="imageText || ''"
        @update:content="(val) => (imageText = val || undefined)"
        @blur="performSaveImageText"
      />

      <!-- Image section with proper spacing -->
      <div v-if="imgUrl.length > 0" class="image-section">
        <div class="image-container">
          <div class="image-wrapper">
            <img :src="imgUrl" :style="imageStyle" />
          </div>
          <FloatImageBtn
            class="pixel-image-position"
            :item-block="props.itemBlock"
            :key="`fab-${props.itemBlock.id}-${effectiveImageWidth}-${effectiveImageHeight}`"
            @dimensions-updated="handleDimensionsUpdated"
          />
        </div>
      </div>
    </div>

    <!-- Footer section that sticks to bottom -->
    <div class="image-block-footer">
      <q-separator></q-separator>
      <div class="footer-content">
        <ItemBlockFooter
          label="ข้อความ"
          :isRequired="Boolean(showImageText)"
          @delete="$emit('delete')"
          @update:isRequired="toggleImageText"
          @duplicate="$emit('duplicate')"
        />
      </div>
    </div>
  </q-card>
</template>
<style scoped>
/* Three-dot menu styles */
.image-top-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -16px;
  margin-bottom: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}

/* Fixed positioning for FloatImageBtn - always at top-left of image */
.pixel-image-position {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  z-index: 10;
  cursor: pointer;
}

.image-wrapper {
  display: inline-block;
  /* ขนาดพอดีกับรูป */
  padding: 0;
  margin: 0;
  /* แสดงให้เห็นขอบ */
  line-height: 0;
  /* ป้องกันช่องว่าง inline */
}

/* Enhanced image container for responsive image display */
.image-container {
  position: relative;
  display: inline-block;
  /* Contain the image and overlay */
  margin: 0 auto;
  /* Center the container */
  max-width: 100%;
  /* Ensure container doesn't exceed parent width */
  overflow: visible;
  margin-top: 20px;
  /* Allow FloatImageBtn to be visible outside bounds */
}

/* Image section with proper spacing */
.image-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;

  /* Add some vertical spacing around image */
  flex-grow: 1;
  /* Allow this section to grow */
}

/* Enhanced styles for the clickable ImageBlock container */
.image-block-container {
  transition: box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  /* Minimal height for empty state */
}

.image-block-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Content area that grows to fill available space */
.image-block-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Footer section that sticks to bottom */
.image-block-footer {
  flex-shrink: 0;
  /* Prevent footer from shrinking */
  margin-top: auto;
  /* Push footer to bottom */
}

.footer-content {
  padding: 8px 0;
  /* Minimal padding for footer */
  min-height: 50px;
  /* Ensure consistent footer height */
  display: flex;
  align-items: center;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .image-container {
    max-width: 95%;
  }

  .image-section {
    padding: 12px 0;
  }
}

@media (max-width: 480px) {
  .image-container {
    max-width: 90%;
  }

  .image-section {
    padding: 8px 0;
  }

  .footer-content {
    min-height: 45px;
  }
}
</style>
