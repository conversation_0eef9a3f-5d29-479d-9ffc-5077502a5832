<template>
  <div class="next-section-block">
    <div class="row items-center justify-between">
      <div class="col-auto">
        <div class="text-subtitle1 text-grey-7">
          <q-icon name="arrow_forward" class="q-mr-sm" />
          หลังจากส่วนนี้เสร็จแล้ว
        </div>
      </div>
      <div class="col-auto">
        <q-select
          :model-value="selectedNextSection"
          :options="sectionDropdownOptions"
          emit-value
          map-options
          outlined
          dense
          placeholder="เลือกส่วนถัดไป"
          style="min-width: 280px"
          @update:model-value="handleSectionSelect"
          :loading="isSaving"
        >
          <template #selected>
            <span v-if="selectedNextSection">{{ getSelectedSectionLabel() }}</span>
            <span v-else class="text-grey-6">ดำเนินการต่อไปยังส่วนถัดไป</span>
          </template>
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { ItemBlock } from 'src/types/models';
import { useGlobalStore } from 'src/stores/global';

interface Props {
  itemBlock: ItemBlock;
  availableSections?: { label: string; value: number }[];
  showSectionDropdowns?: boolean;
}

interface Emits {
  (e: 'update:option-next-section', optionId: number, nextSection: number | null): void;
  (e: 'refresh-assessment', payload: { focusBlockId: number }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const globalStore = useGlobalStore();
const isSaving = ref(false);

// Get the first option from the ItemBlock (NEXTSECTION blocks should have exactly one option)
const firstOption = computed(() => props.itemBlock.options?.[0]);

// Get the current nextSection value from the first option
const selectedNextSection = computed(() => firstOption.value?.nextSection || null);

// Computed property for section dropdown options with Thai formatting
const sectionDropdownOptions = computed(() => {
  if (!props.availableSections || props.availableSections.length === 0) return [];

  // Add default option first
  const options: { label: string; value: number | null }[] = [
    {
      label: 'ดำเนินการต่อไปยังส่วนถัดไป',
      value: null,
    },
  ];

  // Add section options with Thai formatting
  props.availableSections.forEach((section) => {
    options.push({
      label: section.label,
      value: section.value,
    });
  });

  return options;
});

// Get the label for the currently selected section
function getSelectedSectionLabel(): string {
  if (!selectedNextSection.value) return 'ดำเนินการต่อไปยังส่วนถัดไป';

  const selectedOption = sectionDropdownOptions.value.find(
    (option) => option.value === selectedNextSection.value,
  );

  return selectedOption?.label || 'ดำเนินการต่อไปยังส่วนถัดไป';
}

// Handle section selection
async function handleSectionSelect(nextSection: number | null) {
  console.log('🔄 [NextSectionBlock] Handling section selection:', {
    nextSection,
    type: typeof nextSection,
    isNull: nextSection === null,
    optionId: firstOption.value?.id,
  });

  if (!firstOption.value) {
    console.error('❌ No option found for NEXTSECTION block');
    return;
  }

  try {
    isSaving.value = true;
    globalStore.startSaveOperation('Saving section navigation...');

    // Import OptionService for updating the nextSection value
    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // Prepare the update payload for the option's nextSection
    const updatePayload = {
      nextSection: nextSection,
      itemBlockId: props.itemBlock.id,
    };

    console.log('🔄 [NextSectionBlock] Update payload:', {
      updatePayload,
      nextSectionValue: updatePayload.nextSection,
      nextSectionType: typeof updatePayload.nextSection,
    });

    // Update the option with the new nextSection value using updateOptionSilent for proper null handling
    const updatedOption = await optionService.updateOptionSilent(
      firstOption.value.id,
      updatePayload,
    );

    if (updatedOption) {
      globalStore.completeSaveOperation(true, 'Section navigation saved');

      // Emit the update to parent component for state synchronization
      emit('update:option-next-section', firstOption.value.id, nextSection);

      // Trigger assessment refresh to update the UI with the new nextSection value
      emit('refresh-assessment', { focusBlockId: props.itemBlock.id });
    } else {
      globalStore.completeSaveOperation(false, 'Failed to save section navigation');
    }
  } catch (error) {
    console.error('❌ Failed to update next section:', error);
    globalStore.completeSaveOperation(false, 'Failed to save section navigation');
  } finally {
    isSaving.value = false;
  }
}
</script>

<style scoped>
.next-section-block {
  min-height: 60px;
}
</style>
