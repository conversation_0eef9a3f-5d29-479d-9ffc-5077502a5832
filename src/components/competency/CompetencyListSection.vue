<template>
  <div class="q-mb-md">
    <div class="row items-center justify-between q-mb-md">
      <div class="row items-center">
        <q-icon
          :name="isOpen ? 'keyboard_arrow_down' : 'keyboard_arrow_right'"
          color="dark"
          size="sm"
          class="q-mr-sm"
          @click="toggleOpen"
          style="cursor: pointer"
        />
        <q-icon name="folder" color="dark" size="sm" />
        <span class="text-weight-bold q-ml-sm">{{ title }}</span>
      </div>
      <q-btn round color="accent" icon="add" dense size="sm" class="q-mr-md" />
    </div>

    <!-- Default items display -->
    <div v-if="isOpen && showItems" class="bg-white q-mt-sm">
      <q-list v-if="items.length > 0">
        <q-item v-for="(item, idx) in items" :key="idx">
          <q-item-section>
            <span class="q-ml-xl">{{ item }}</span>
          </q-item-section>
          <q-item-section side>
            <q-btn flat round icon="delete" color="negative" size="sm" @click="remove(idx)" />
          </q-item-section>
        </q-item>
      </q-list>
      <div v-else class="q-pa-md text-grey-6">ไม่มีรายการ</div>
    </div>

    <!-- Custom content slot -->
    <div v-if="isOpen && !showItems">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRefs, withDefaults } from 'vue';

const props = withDefaults(
  defineProps<{
    title: string;
    items: string[];
    modelValue: boolean;
    showItems?: boolean;
  }>(),
  {
    showItems: true,
  },
);
const emit = defineEmits(['update:modelValue', 'remove']);

const { modelValue, showItems } = toRefs(props);
const isOpen = ref(modelValue.value);

watch(modelValue, (val) => {
  isOpen.value = val;
});

function toggleOpen() {
  isOpen.value = !isOpen.value;
  emit('update:modelValue', isOpen.value);
}

function remove(idx: number) {
  emit('remove', idx);
}
</script>

<style scoped></style>
