<template>
  <q-page padding>
    <div class="text-h5 q-mb-sm">จัดการแแผนพัฒนารายบุคคล</div>
    <div class="text-h6 q-mb-md">ข้อมูลบุคลากร</div>

    <div class="column items-start">
      <UserProfile class="q-mb-md" />
      <TabNavigation :tabs="tabItems" v-model="selectedTab" />
    </div>
    <div v-if="selectedTab === 'core'">
      <div v-if="!selectedSkill" style="display: flex; gap: 24px">
        <TableManagePlan
          :rows="rows"
          :columns="columns"
          :selectedRowId="selectedRowId1"
          @set-selected-row-id="setSelectedRowId1"
          @select-row="(row: SkillRow) => handleSelectSkill(row, 1)"
        />
        <TableManagePlan
          :rows="rows1"
          :columns="columns1"
          :selectedRowId="selectedRowId2"
          @set-selected-row-id="setSelectedRowId2"
          @select-row="(row: SkillRow) => handleSelectSkill(row, 2)"
        />
      </div>
      <div v-else>
        <div style="display: flex; gap: 24px; align-items: flex-start">
          <div style="flex: 1; min-width: 0">
            <TableManagePlan
              :rows="rows"
              :columns="columns"
              :selectedRowId="selectedRowId1"
              @set-selected-row-id="setSelectedRowId1"
              @select-row="(row: SkillRow) => handleSelectSkill(row, 1)"
            />
            <TableManagePlan
              :rows="rows1"
              :columns="columns1"
              :selectedRowId="selectedRowId2"
              @set-selected-row-id="setSelectedRowId2"
              @select-row="(row: SkillRow) => handleSelectSkill(row, 2)"
            />
          </div>
          <div style="width: 620px; min-width: 520px">
            <div v-if="selectedSkill" style="margin-bottom: 16px">
              <SkillCard :skill="selectedSkill" @close="handleClearSkill" />
            </div>
            <CourseCard />
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserProfile from 'src/components/common/UserProfile.vue';
import { ref } from 'vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';
import TableManagePlan from 'src/components/plan/TableManagePlan.vue';
import SkillCard from 'src/components/plan/SkillCard.vue';
import CourseCard from 'src/components/plan/CourseCard.vue';
const TAB_KEY = 'selected-plan-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'core');

const tabItems = [
  { label: 'แผนพัฒนา', value: 'core' },
  { label: 'เส้นทางพัฒนา', value: 'academic' },
  { label: 'ติดตามหลักฐาน', value: 'support' },
  { label: 'ความรู้และทักษะ', value: 'admin' },
];
const columns = [
  {
    name: 'name',
    label: 'ความรู้และทักษะทั่วไป',
    field: 'name',
    align: 'left',
  },
];

const rows = [
  { id: 1, name: 'การอ่าน การตีความ และการใช้กฎระเบียบทั่วไปของมหาวิทยาลัย' },
  { id: 2, name: 'การเขียนหนังสือหรือเอกสารราชการ' },
  { id: 3, name: 'การพัฒนาทักษะภาษาอังกฤษ' },
  { id: 4, name: 'การทำงานเป็นทีม' },
  { id: 5, name: 'การทำงานเป็นทีม' },
];
const columns1 = [
  {
    name: 'name',
    label: 'ความรู้และทักษะเฉพาะด้านวิชาการ',
    field: 'name',
    align: 'left',
  },
];

const rows1 = [
  { id: 1, name: 'การจัดการเรียนการสอน การให้คำปรึกษา การวัด และประเมินผล' },
  { id: 2, name: 'การพัฒนาสื่อการสอนหรือการจัดการเรียนการสอนรูปแบบใหม่' },
  { id: 3, name: 'การบริหารโครงการวิจัยและการบริการวิชาการ' },
  { id: 4, name: 'แนวทางการพัฒนาผลงานทางวิชาการ' },
  { id: 5, name: 'แนวทางการพัฒนาผลงานทางวิชาการ' },
];

const selectedSkill = ref<null | { id: number; name: string }>(null);
const selectedRowId1 = ref<number | null>(null);
const selectedRowId2 = ref<number | null>(null);

type SkillRow = { id: number; name: string };

function handleSelectSkill(skill: SkillRow, table: 1 | 2) {
  selectedSkill.value = skill;
  if (table === 1) {
    selectedRowId2.value = null;
  } else {
    selectedRowId1.value = null;
  }
}
function handleClearSkill() {
  selectedSkill.value = null;
  selectedRowId1.value = null;
  selectedRowId2.value = null;
}

function setSelectedRowId1(id: number | null) {
  selectedRowId1.value = id;
}
function setSelectedRowId2(id: number | null) {
  selectedRowId2.value = id;
}
</script>

<style scoped lang="scss">
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 320px;
  max-width: 420px;
}
</style>
