<template>
  <q-page padding>
    <div v-if="loading" class="text-center">
      <q-spinner size="lg" />
      <div class="q-mt-sm">กำลังโหลดข้อมูล...</div>
    </div>
    <div v-else-if="developmentPlan">
      <div class="text-h5 q-mb-md text-weight-bold">
        จัดการแผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="text-body1 q-mb-md text-weight-bold">
        แผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">สถานะ :</span>
        <StatusCapsule :published="developmentPlan.isActive" class="q-mr-sm" />
        <q-btn class="q-ml-sm bg-black text-white">เผยแพร่</q-btn>
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="หน่วยงาน/ส่วนงาน" :options="searchOptions" />
      </div>
      <div v-if="selectedTab === 'position'" class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="ประเภทสายงาน" :options="searchTypeOptions" />
        <SearchDropDownBar placeholder="ตำแหน่ง" :options="searchPositionOptions" />
        <SearchDropDownBar placeholder="ระดับ" :options="searchRankOptions" />
      </div>
      <div class="q-mb-md">
        <TabNavigation
          v-model="selectedTab"
          :tabs="devPlanTabs.map((tab) => ({ label: tab.label, value: tab.name }))"
          class="q-mb-md"
        />
      </div>
    </div>
    <div v-else class="text-center text-grey-6">ไม่พบข้อมูลแผนพัฒนา</div>

    <div v-if="!loading && developmentPlan">
      <div v-if="selectedTab === 'all'">
        <div class="q-mb-md">
          <div class="row items-center justify-between q-mb-md">
            <span class="text-weight-bold">ความรู้และทักษะทั่วไปของบุคลากร</span>
            <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
          </div>
          <CompetencyListSection
            title="อายุการปฏิบัติงาน 1 - 2 ปี"
            :items="competencyList"
            v-model="isListOpen"
            @remove="removeCompetency"
          />
          <CompetencyListSection
            title="อายุการปฏิบัติงาน 3 - 5 ปี"
            :items="competencyList35"
            v-model="isListOpen35"
            @remove="removeCompetency35"
          />
        </div>
      </div>
      <div v-else-if="selectedTab === 'admin'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะทั่วไปของผู้บริหาร</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <CompetencyListSection
          title="สมรรถนะทั่วไปผู้บริหาร"
          :items="competencyListAdmin"
          v-model="isListOpenAdmin"
          @remove="removeCompetencyAdmin"
        />
      </div>
      <div v-else-if="selectedTab === 'academic'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านสายวิชาการ</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <CompetencyListSection
          title="สมรรถนะสายวิชาการ"
          :items="competencyListAcademic"
          v-model="isListOpenAcademic"
          @remove="removeCompetencyAcademic"
        />
      </div>
      <div v-else-if="selectedTab === 'support'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านสายสนับสนุน</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <CompetencyListSection
          title="สมรรถนะสายสนับสนุนวิชาการ"
          :items="competencyListSupport"
          v-model="isListOpenSupport"
          @remove="removeCompetencySupport"
        />
      </div>
      <div v-else-if="selectedTab === 'adminonly'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านผู้บริหาร</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>
        <CompetencyListSection
          title="สมรรถนะเฉพาะด้านผู้บริหาร"
          :items="competencyListAdminOnly"
          v-model="isListOpenAdminOnly"
          @remove="removeCompetencyAdminOnly"
        />
      </div>
      <div v-else-if="selectedTab === 'position'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านของตำแหน่ง</span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>

        <!-- Hierarchical Position Competencies -->
        <div
          v-for="(position, positionIndex) in hierarchicalPositionCompetencies"
          :key="positionIndex"
          class="q-mb-md"
        >
          <CompetencyListSection
            :title="position.title"
            :items="[]"
            v-model="position.isOpen"
            :show-items="false"
          >
            <template #content>
              <div v-if="position.isOpen" class="q-pl-md">
                <div
                  v-for="(experienceGroup, expIndex) in position.experienceGroups"
                  :key="expIndex"
                  class="q-mb-sm"
                >
                  <CompetencyListSection
                    :title="experienceGroup.title"
                    :items="experienceGroup.skills"
                    v-model="experienceGroup.isOpen"
                    @remove="(idx) => removePositionSkill(positionIndex, expIndex, idx)"
                  />
                </div>
              </div>
            </template>
          </CompetencyListSection>
        </div>
      </div>
      <div v-else>
        <div class="q-pa-md bg-grey-3 q-mb-md">เนื้อหาอื่น ๆ (mock)</div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import CompetencyListSection from 'src/components/competency/CompetencyListSection.vue';
import { useQuasar } from 'quasar';
import StatusCapsule from 'src/components/common/StatusCapsule.vue';
import { useRoute } from 'vue-router';
import type { DevelopmentPlan } from 'src/types/idp';
import { useGlobalStore } from 'src/stores/global';
import SearchDropDownBar from 'src/components/SearchDropDownBar.vue';

import { devPlanTabs } from 'src/data/devPlanTabs';

import TabNavigation from 'src/components/common/TabNavigation.vue';

// Mock options for SearchDropDownBar
const searchOptions = [
  'กองบริหารและพัฒนาทรัพยากรบุคคล',
  'สํานักคอมพิวเตอร์',
  'กองแผนงาน',
  'กองกิจการนิสิต',
];

const searchTypeOptions = ['ทุกสายงาน', 'สายวิชาการ', 'สายสนับสนุนวิชาการ'];

const searchPositionOptions = [
  'ทุกตําแหน่ง',
  'นักวิชาการเงินและบัญชี',
  'นักวิชาการศึกษา',
  'นักวิชาการคอมพิวเตอร์',
  'นักวิชาการพัสดุ',
];

const searchRankOptions = ['ปฏิบัติการ', 'ชํานาญการ', 'ชํานาญการพิเศษ', 'ทรงคุณวุฒิ'];

const selectedTab = ref(devPlanTabs[0]?.name ?? '');

// Mock competency list for demo
const competencyList = ref([
  'การอ่าน การตีความ และการใช้กฎระเบียบทั่วไปของมหาวิทยาลัย',
  'การเขียนหนังสือหรือเอกสารราชการ',
  'การพัฒนาทักษะภาษาอังกฤษ',
  'การทำงานเป็นทีม',
  'จริยธรรมและวินัยของผู้ปฏิบัติงานในมหาวิทยาลัย',
  'กิจกรรมเสริมสร้างค่านิยมองค์กร',
  'การพัฒนาตามสมรรถนะหลัก',
  'ความเข้าใจในสถานภาพของมหาวิทยาลัยและความจำเป็นในการเพิ่มขีดความสามารถในการแข่งขัน',
]);
const competencyList35 = ref([
  'การวางแผนและบริหารโครงการขนาดเล็ก',
  'การสื่อสารและการนำเสนอในที่ประชุม',
  'การแก้ไขปัญหาเฉพาะหน้า',
  'การพัฒนาทักษะการใช้เทคโนโลยีสารสนเทศ',
  'การสร้างเครือข่ายความร่วมมือกับหน่วยงานอื่น',
]);
const competencyListAdmin = ref([
  'ภาวะผู้นำและการตัดสินใจ',
  'การบริหารจัดการองค์กร',
  'การสื่อสารและการโน้มน้าวใจ',
  'การวางแผนกลยุทธ์',
]);
const competencyListAcademic = ref([
  'การวิจัยและพัฒนาองค์ความรู้',
  'การสอนและการถ่ายทอดความรู้',
  'การประเมินผลการเรียนรู้',
  'การสร้างนวัตกรรมทางการศึกษา',
]);
const competencyListSupport = ref([
  'การให้บริการและสนับสนุนงานวิชาการ',
  'การบริหารจัดการสำนักงาน',
  'การประสานงานและการสื่อสารภายในองค์กร',
  'การใช้เทคโนโลยีสารสนเทศในงานสนับสนุน',
]);
const competencyListAdminOnly = ref([
  'การกำหนดนโยบายและทิศทางองค์กร',
  'การบริหารความเสี่ยง',
  'การบริหารงบประมาณและทรัพยากร',
  'การพัฒนาภาวะผู้นำขั้นสูง',
]);
// Hierarchical position competencies structure
const hierarchicalPositionCompetencies = ref([
  {
    title: 'นักวิชาการ',
    isOpen: true,
    experienceGroups: [
      {
        title: 'อายุงาน 1-2 ปี',
        isOpen: true,
        skills: [
          'การอ่านและตีความกฎระเบียบเบื้องต้น',
          'การเขียนเอกสารราชการพื้นฐาน',
          'การใช้งานระบบสารสนเทศเบื้องต้น',
          'การทำงานเป็นทีมในระดับหน่วยงาน',
        ],
      },
      {
        title: 'อายุงาน 3-5 ปี',
        isOpen: true,
        skills: [
          'การวางแผนงานระยะสั้น',
          'การประสานงานกับหน่วยงานภายนอก',
          'การแก้ไขปัญหาเฉพาะด้าน',
          'การพัฒนาความรู้เฉพาะทาง',
        ],
      },
      {
        title: 'อายุงาน 6+ ปี',
        isOpen: false,
        skills: [
          'การกำกับดูแลงานในระดับกลุม',
          'การให้คำปรึกษาเฉพาะด้าน',
          'การพัฒนาระบบงานใหม่',
          'การถ่ายทอดความรู้สู่ผู้ใหม่',
        ],
      },
    ],
  },
  {
    title: 'เจ้าหน้าที่บริหารงานทั่วไป',
    isOpen: false,
    experienceGroups: [
      {
        title: 'อายุงาน 1-2 ปี',
        isOpen: true,
        skills: [
          'การดูแลเอกสารและการจัดเก็บ',
          'การต้อนรับและให้บริการผู้ติดต่อ',
          'การจัดการตารางงานและนัดหมาย',
          'การใช้โปรแกรมสำนักงานพื้นฐาน',
        ],
      },
      {
        title: 'อายุงาน 3-5 ปี',
        isOpen: true,
        skills: [
          'การดูแลและควบคุมงานธุรการ',
          'การจัดการข้อมูลและรายงาน',
          'การประสานงานระหว่างหน่วยงาน',
          'การปฏิบัติงานตามระเบียบราชการ',
        ],
      },
    ],
  },
  {
    title: 'นักวิจัย',
    isOpen: false,
    experienceGroups: [
      {
        title: 'อายุงาน 1-2 ปี',
        isOpen: true,
        skills: [
          'การค้นคว้าข้อมูลและเอกสารอ้างอิง',
          'การใช้เครื่องมือวิจัยเบื้องต้น',
          'การเขียนรายงานการวิจัย',
          'การวิเคราะห์ข้อมูลเชิงพรรณนา',
        ],
      },
      {
        title: 'อายุงาน 3-5 ปี',
        isOpen: true,
        skills: [
          'การออกแบบการวิจัยเฉพาะด้าน',
          'การวิเคราะห์ข้อมูลทางสถิติ',
          'การเขียนบทความวิชาการ',
          'การนำเสนอผลงานวิจัย',
        ],
      },
    ],
  },
  {
    title: 'เจ้าหน้าที่เทคนิค',
    isOpen: false,
    experienceGroups: [
      {
        title: 'อายุงาน 1-2 ปี',
        isOpen: true,
        skills: [
          'การปฏิบัติงานเทคนิคพื้นฐาน',
          'การใช้เครื่องมือและอุปกรณ์',
          'การบำรุงรักษาเบื้องต้น',
          'การปฏิบัติตามมาตรฐานความปลอดภัย',
        ],
      },
      {
        title: 'อายุงาน 3-5 ปี',
        isOpen: true,
        skills: [
          'การแก้ไขปัญหาเทคนิคขั้นกลาง',
          'การควบคุมคุณภาพงาน',
          'การพัฒนาปรับปรุงกระบวนการ',
          'การให้คำแนะนำเทคนิค',
        ],
      },
    ],
  },
]);

const isListOpen = ref(true);
const isListOpen35 = ref(true);
const isListOpenAdmin = ref(true);
const isListOpenAcademic = ref(true);
const isListOpenSupport = ref(true);
const isListOpenAdminOnly = ref(true);
const $q = useQuasar();

function removeCompetency(idx: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบรายการนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    competencyList.value.splice(idx, 1);
  });
}
function removeCompetency35(idx: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบรายการนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    competencyList35.value.splice(idx, 1);
  });
}
function removeCompetencyAdmin(idx: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบรายการนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    competencyListAdmin.value.splice(idx, 1);
  });
}
function removeCompetencyAcademic(idx: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบรายการนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    competencyListAcademic.value.splice(idx, 1);
  });
}
function removeCompetencySupport(idx: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบรายการนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    competencyListSupport.value.splice(idx, 1);
  });
}
function removeCompetencyAdminOnly(idx: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบรายการนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    competencyListAdminOnly.value.splice(idx, 1);
  });
}

function removePositionSkill(positionIndex: number, expIndex: number, skillIndex: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบทักษะนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const position = hierarchicalPositionCompetencies.value[positionIndex];
    const experienceGroup = position?.experienceGroups[expIndex];
    if (experienceGroup) {
      experienceGroup.skills.splice(skillIndex, 1);
    }
  });
}

// ตัวอย่าง mock tab name ที่รองรับ overview, plan, result
// devPlanTabs ในที่นี้ควรมี name: 'overview', 'plan', 'result' อย่างน้อย 1 อัน

const route = useRoute();
const globalStore = useGlobalStore();
const loading = ref(false);
const developmentPlan = ref<DevelopmentPlan | null>(null);

// Mock data - same as in DevPlanManagement.vue
const mockCompetencies: DevelopmentPlan[] = [
  {
    id: 1,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2567',
    description: 'ความสามารถในการวางแผน ดำเนินการ และควบคุมโครงการให้สำเร็จตามเป้าหมาย',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 2,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2568',
    description: 'ทักษะในการสื่อสาร การนำเสนอข้อมูล และการประสานงานกับผู้อื่น',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 3,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2569',
    description: 'ความสามารถในการวิเคราะห์ปัญหา ประมวลผลข้อมูล และหาแนวทางแก้ไข',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 4,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2570',
    description: 'ความสามารถในการเป็นผู้นำ การมอบหมายงาน และการสร้างแรงบันดาลใจให้ทีม',
    isActive: false,
    originalPlan: null,
  },
  {
    id: 5,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2571',
    description: 'ทักษะการใช้เทคโนโลยี การใช้งานซอฟต์แวร์ และการปรับตัวกับเทคโนโลยีใหม่',
    isActive: false,
    originalPlan: null,
  },
];

// Function to extract year from plan name
const extractYearFromPlanName = (planName: string): string => {
  const yearMatch = planName.match(/ปี\s*(\d{4})/);
  return yearMatch?.[1] ?? 'XXXX';
};

// Function to update global store with dynamic breadcrumb
const updateDevelopmentPlanBreadcrumb = (plan: DevelopmentPlan | null) => {
  if (plan) {
    const year = extractYearFromPlanName(plan.name);
    globalStore.setDevelopmentPlanYear(year);
  }
};

// Function to fetch development plan by ID
const fetchDevelopmentPlan = (id: number) => {
  loading.value = true;
  try {
    // In a real application, this would be an API call
    // For now, using mock data
    const plan = mockCompetencies.find((p) => p.id === id);
    developmentPlan.value = plan || null;
    updateDevelopmentPlanBreadcrumb(plan || null);
  } catch (error) {
    console.error('Error fetching development plan:', error);
    developmentPlan.value = null;
    updateDevelopmentPlanBreadcrumb(null);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  const planId = Number(route.params.id);
  if (planId && !isNaN(planId)) {
    fetchDevelopmentPlan(planId);
  }
});

// Watch for route parameter changes
watch(
  () => route.params.id,
  (newId) => {
    const planId = Number(newId);
    if (planId && !isNaN(planId)) {
      fetchDevelopmentPlan(planId);
    }
  },
);

// Clear breadcrumb when component unmounts
onUnmounted(() => {
  globalStore.clearDevelopmentPlanYear();
});
</script>

<style scoped></style>
