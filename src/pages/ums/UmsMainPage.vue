<template>
  <q-page class="column no-wrap">
    <div class="q-pb-none q-pt-md q-pl-md q-pr-md">
      <TopHeaderTable :title="dynamicTitle">
        <template #tab>
          <div class="row items-center justify-between q-mt-sm">
            <TabNavigation :tabs="tabNavigationItems" v-model="activeTabName" />
          </div>
        </template>
      </TopHeaderTable>
    </div>

    <div v-if="activeTabName" class="col">
      <q-tab-panels v-model="activeTabName" animated class="full-height">
        <q-tab-panel
          v-for="(tab, index) in defaultUmsTabsMenu"
          :key="index"
          :name="tab.name"
          class="q-pa-none full-height"
        >
          <component
            :is="(componentMap as Record<string, any>)[tab.name as string]"
            v-if="activeTabName === tab.name"
          />
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { defaultUmsTabsMenu } from 'src/data/menu';
import { defineAsyncComponent, computed, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';

const route = useRoute();
const router = useRouter();
const TAB_KEY = 'user-role-management-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'roles');

watch(selectedTab, (val) => {
  localStorage.setItem(TAB_KEY, val);
});

// Convert QTabProps[] to TabItem[] for TabNavigation component
const tabNavigationItems = computed(() =>
  defaultUmsTabsMenu.map((tab) => ({
    label: String(tab.label || ''),
    value: String(tab.name || ''),
  })),
);

const activeTabName = computed({
  get: (): string => {
    // Get tab name by removing '#' from route.hash
    const hashTab = route.hash ? route.hash.substring(1) : '';
    const defaultTab = String(defaultUmsTabsMenu[0]?.name || '');
    return hashTab || selectedTab.value || defaultTab;
  },
  set: (newName: string) => {
    // Update route.hash when tab changes
    // Ensure newName is not empty and hash actually needs changing
    if (newName && route.hash !== `#${newName}`) {
      void router.push({ hash: '#' + newName });
    }
    // Update selectedTab and localStorage
    selectedTab.value = newName;
    localStorage.setItem(TAB_KEY, newName);
  },
});

// Watch for route hash changes to update selectedTab
watch(
  () => route.hash,
  (newHash) => {
    const tabName = newHash ? newHash.substring(1) : '';
    if (tabName && tabName !== selectedTab.value) {
      selectedTab.value = tabName;
      localStorage.setItem(TAB_KEY, tabName);
    }
  },
  { immediate: true },
);

const componentMap = {
  users: defineAsyncComponent(() => import('./tabs/UserManagementView.vue')),
  roles: defineAsyncComponent(() => import('./tabs/RoleManagementView.vue')),
} as const;

// Dynamic title based on active tab
const dynamicTitle = computed(() => {
  const titleMap: Record<string, string> = {
    users: 'จัดการผู้ใช้งาน',
    roles: 'จัดการบทบาท',
  };
  return titleMap[activeTabName.value] || 'การจัดบทบาทและบุคลากร';
});
</script>

<style scoped>
.full-height {
  height: 100%;
}

:deep(.q-tab-panel) {
  padding: 0 !important;
}

:deep(.q-page) {
  padding-block: 0 !important; /* ยกเลิก padding บน–ล่าง */
  margin-block: 0 !important; /* ยกเลิก margin บน–ล่าง */
}
</style>
