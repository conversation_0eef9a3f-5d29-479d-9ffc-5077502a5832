<template>
  <q-page padding class="q-gutter-y-lg">
    <div class="row items-center q-gutter-x-md">
      <div class="text-h6">จัดการ{{ criteriaName }}</div>
    </div>

    <TopHeaderTable
      title=""
      create-button-label="เพิ่มช่วงอายุงานใหม่"
      @search="onSearchUpdate"
      @create="onClickAdd"
      class="justify-end"
    />

    <q-table
      :rows="ageWorkCriteriaStore.ageWorks"
      :columns="ageWorkColumns"
      :loading="ageWorkCriteriaStore.loading"
      :pagination="{
        page: ageWorkCriteriaStore.currentPage,
        rowsPerPage: 10,
        rowsNumber: ageWorkCriteriaStore.totalRecords,
      }"
      @request="onRequest"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />

            <q-btn dense unelevated class="view-icon" icon="visibility" @click="onClickView(row)" />
            <q-btn dense unelevated class="del-icon" icon="delete" @click="onClickDelete(row)" />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import AgeWorkForm from 'src/components/idp/AgeWorkForm.vue';
import { useAgeWorkCriteriaStore } from 'src/stores/age_work_criteria';
import type { QTableProps } from 'quasar';

interface AgeWork {
  id: number;
  name: string;
  startYear: number;
  endYear: number;
  ageWorkCriteriaId: number;
}

const $q = useQuasar();
const route = useRoute();
const ageWorkCriteriaStore = useAgeWorkCriteriaStore();

const criteriaId = computed(() => Number(route.params.criteriaId));
const criteriaName = ref('');

const ageWorkColumns = [
  { name: 'id', label: 'รหัส', align: 'left' as const, field: 'id', sortable: true },
  {
    name: 'name',
    label: 'อายุการปฏิบัติงาน',
    align: 'center' as const,
    field: 'name',
    sortable: true,
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: '', sortable: false },
];

const searchKeyword = ref('');

onMounted(() => {
  void loadCriteriaInfo();
  void loadAgeWorks();
});

const loadCriteriaInfo = async () => {
  try {
    const criteria = await ageWorkCriteriaStore.fetchOneAgeWorkCriteria(criteriaId.value);
    criteriaName.value = criteria.name;
  } catch (error: unknown) {
    console.error('Error loading criteria info:', error);
  }
};

const loadAgeWorks = async () => {
  try {
    await ageWorkCriteriaStore.fetchAgeWorksByCriteria(
      criteriaId.value,
      {
        page: 1,
        rowsPerPage: 10,
        sortBy: 'id',
        descending: false,
      },
      searchKeyword.value,
    );
  } catch (error: unknown) {
    console.error('Error loading age works:', error);
  }
};

const onRequest = async (props: { pagination: QTableProps['pagination'] }) => {
  try {
    await ageWorkCriteriaStore.fetchAgeWorksByCriteria(
      criteriaId.value,
      props.pagination,
      searchKeyword.value,
    );
  } catch (error: unknown) {
    console.error('Error loading age works:', error);
  }
};

const onClickView = (row: AgeWork) => {
  console.log('View details for:', row);
  $q.notify({
    type: 'info',
    message: `ดูรายละเอียดของ ${row.name}`,
  });
};

const onClickEdit = (row: AgeWork) => {
  $q.dialog({
    component: AgeWorkForm,
    componentProps: {
      formData: row,
      criteriaId: criteriaId.value,
    },
    persistent: true,
  })
    .onOk((data: Omit<AgeWork, 'id'>) => {
      void (async () => {
        try {
          console.log('Update age work:', data);
          $q.notify({ type: 'positive', message: 'อัปเดตอายุงานสำเร็จ' });
          await loadAgeWorks();
        } catch (error: unknown) {
          console.error('Error updating age work:', error);
        }
      })();
    })
    .onCancel(() => {})
    .onDismiss(() => {});
};

const onClickDelete = (row: AgeWork) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณต้องการลบข้อมูล "${row.name}" ใช่หรือไม่?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    void (async () => {
      try {
        console.log('Delete age work:', row);
        $q.notify({ type: 'positive', message: 'ลบอายุงานสำเร็จ' });
        await loadAgeWorks();
      } catch (error: unknown) {
        console.error('Error deleting age work:', error);
      }
    })();
  });
};

//TODO: implement crud operation
const onClickAdd = () => {
  $q.dialog({
    component: AgeWorkForm,
    componentProps: {
      criteriaId: criteriaId.value,
    },
    persistent: true,
  })
    .onOk((data: Omit<AgeWork, 'id'>) => {
      void (async () => {
        try {
          console.log('Create age work:', data);
          $q.notify({ type: 'positive', message: 'สร้างอายุงานสำเร็จ' });
          await loadAgeWorks();
        } catch (error: unknown) {
          console.error('Error creating age work:', error);
        }
      })();
    })
    .onCancel(() => {})
    .onDismiss(() => {});
};

const onSearchUpdate = (keyword: string) => {
  searchKeyword.value = keyword;
  void loadAgeWorks();
};
</script>

<style scoped>
:deep(.top-header-table .row) {
  justify-content: flex-end !important;
}
</style>
