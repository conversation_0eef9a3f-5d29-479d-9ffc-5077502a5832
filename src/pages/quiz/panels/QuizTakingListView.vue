<template>
  <div class="quiz-taking-list-container">
    <!-- Search Bar -->
    <div class="search-container q-mb-md" style="display: flex; justify-content: flex-end;">
      <SearchBar @search="handleSearch" />
    </div>

    <!-- Error Banner -->
    <q-banner
      v-if="dashboardStore.error"
      inline-actions
      class="text-white bg-red q-mb-md rounded-borders"
    >
      <template v-slot:avatar>
        <q-icon name="error_outline" color="white" />
      </template>
      {{ dashboardStore.error }}
      <template v-slot:action>
        <q-btn flat color="white" label="Retry" @click="retryFetch" :loading="isLoading" />
        <q-btn flat color="white" label="Clear" @click="dashboardStore.clearError" />
      </template>
    </q-banner>

    <!-- QTable -->
    <q-table
      :rows="tableRows"
      :columns="columns"
      row-key="id"
      v-model:pagination="paginationState"
      @request="handleTableRequest"
      :rows-per-page-options="[5, 10, 15, 25, 50]"
      binary-state-sort
      flat
      bordered
      separator="cell"
      class="q-mt-md animate-table"
      table-header-class="bg-primary text-black text-h6"
      :loading-delay="100"
      :table-style="{ transition: 'all 0.3s ease-out' }"
      :loading-spinner-size="40"
    >
      <template v-slot:body-cell-userName="{ row }">
        <q-td class="text-left">{{ row.userName }}</q-td>
      </template>

      <template v-slot:body-cell-date="{ row }">
        <q-td class="text-center">{{
          new Date(row.date).toLocaleString('th-TH', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          })
        }}</q-td>
      </template>

      <template v-slot:body-cell-score="{ row }">
        <q-td
          :class="[
            'text-center',
            row.score < (dashboardStore.assessmentMeta?.highestScore ?? 100) / 2
              ? 'text-negative'
              : 'text-positive',
          ]"
        >
          {{ row.score }}
        </q-td>
      </template>

      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <q-btn
            dense
            unelevated
            class="edit-graph-icon"
            icon="article"
            @click="viewUserAnswer(row.id)"
            aria-label="View Answers"
          >
            <q-tooltip>ดูคำตอบ</q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <template v-slot:no-data="{ icon, message, filter }">
        <div class="full-width row flex-center text-accent q-gutter-sm q-pa-md">
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
          <span>
            {{
              props.assessmentId === null && !isLoading
                ? 'Please select an assessment first.'
                : message
            }}
          </span>
        </div>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import type { QTableProps } from 'quasar';

import type { DataParams } from 'src/types/data';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';
import SearchBar from 'src/components/SearchBar.vue';

const props = defineProps({
  assessmentId: {
    type: Number as PropType<number | null>,
    required: true,
  },
});

const dashboardStore = useQuizDashboardStore();
const router = useRouter();
const paginationState = ref<Required<QTableProps['pagination']>>({
  sortBy: 'date',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// --- Computed Properties ---
const isLoading = computed(() => {
  return dashboardStore.isLoadingParticipants;
});

const tableRows = computed(() => {
  return dashboardStore.participants?.data || [];
});

// --- Columns Definition ---
const columns: QTableProps['columns'] = [
  {
    name: 'id',
    label: 'รหัส',
    align: 'center',
    field: 'id',
    sortable: true,
    required: true,
  },
  {
    name: 'date',
    label: 'เวลาที่เริ่มทำ',
    align: 'center',
    field: 'date',
    sortable: true,
    required: true,
  },
  {
    name: 'userName',
    label: 'ชื่อผู้ทำแบบทดสอบ',
    align: 'left',
    field: 'userName',
    sortable: true,
    required: true,
  },
  {
    name: 'score',
    label: 'คะแนน',
    align: 'center',
    field: 'score',
    sortable: true,
    required: true,
  },
  {
    name: 'actions',
    label: 'กระดาษคำตอบ',
    align: 'center',
    field: () => '',
  },
];

// --- Methods ---
async function handleTableRequest(requestProps: {
  pagination: QTableProps['pagination'];
  filter?: string;
}) {
  const newPagination = requestProps.pagination;
  console.log('[QuizTakerList] handleTableRequest triggered. Assessment ID:', props.assessmentId);

  if (props.assessmentId === null) {
    console.warn('[QuizTakerList] handleTableRequest: assessmentId is null, aborting fetch.');
    return;
  }

  // Update local pagination state
  if (paginationState.value && newPagination) {
    paginationState.value.page = newPagination.page ?? 1;
    paginationState.value.rowsPerPage = newPagination.rowsPerPage ?? 10;
    paginationState.value.sortBy = newPagination.sortBy ?? null;
    paginationState.value.descending = newPagination.descending ?? false;
  }
  const params: DataParams = {
    page: paginationState.value?.page ?? 1,
    limit: paginationState.value?.rowsPerPage ?? 10,
    sortBy: paginationState.value?.sortBy ?? 'date',
    order: paginationState.value?.descending ? 'DESC' : 'ASC',
    search: null,
  };

  console.log('[QuizTakerList] Calling fetchParticipants with params:', params);
  await dashboardStore.fetchParticipants(props.assessmentId, params);
}

async function retryFetch() {
  console.log('[QuizTakerList] retryFetch called.');
  dashboardStore.clearError();
  if (props.assessmentId !== null && paginationState.value) {
    await handleTableRequest({ pagination: paginationState.value });
  }
}

async function viewUserAnswer(participantId: number) {
  console.log('[QuizTakerList] viewUserAnswer called for participant:', participantId);
  // Navigate to participant details page
  await router.push({
    name: 'ParticipantDetails',
    params: { participantId: participantId.toString() },
  });
}

// Search handler
const handleSearch = async (searchValue: string) => {
  if (props.assessmentId === null || !paginationState.value) return;

  const params: DataParams = {
    page: 1, // Reset to first page on search
    limit: paginationState.value.rowsPerPage,
    sortBy: paginationState.value.sortBy ?? 'date',
    order: paginationState.value.descending ? 'DESC' : 'ASC',
    search: searchValue,
  };

  await dashboardStore.fetchParticipants(props.assessmentId, params);
};

// --- Watchers ---
watch(
  () => props.assessmentId,
  async (newAssessmentId, oldAssessmentId) => {
    console.log(
      `[QuizTakerList] assessmentId watcher triggered. New: ${newAssessmentId}, Old: ${oldAssessmentId}`,
    );
    if (newAssessmentId !== oldAssessmentId) {
      if (paginationState.value) {
        paginationState.value.page = 1;
      }
      if (newAssessmentId !== null && paginationState.value) {
        await handleTableRequest({ pagination: paginationState.value });
      }
    }
  },
  { immediate: true },
);

watch(
  () => dashboardStore.participants,
  (newParticipants) => {
    console.log('[QuizTakerList] participants watcher triggered');
    if (paginationState.value) {
      if (newParticipants) {
        paginationState.value.rowsNumber = newParticipants.total;
        paginationState.value.page = newParticipants.curPage;
      } else {
        paginationState.value.rowsNumber = 0;
      }
    }
  },
  { deep: true },
);

// --- Lifecycle Hooks ---
onMounted(() => {
  console.log(`[QuizTakerList] Component mounted. Initial assessmentId: ${props.assessmentId}`);
});
</script>
<style scoped lang="scss">
</style>
