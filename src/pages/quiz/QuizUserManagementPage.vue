<template>
  <q-page padding>
    <TopHeaderTable title="แบบทดสอบทั้งหมด" :show-create-button="false" @search="onSearchUpdate" />

    <q-table
      :rows="rows"
      :columns="quizUserColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      :loading="Loading.isActive"
      v-model:pagination="pagination"
      @request="handleRequest"
      binary-state-sort
    >
      <template v-slot:body-cell-link="{ row }">
        <q-td class="text-center">
          <q-btn flat dense icon="link" :disable="!row.assessmentLink" aria-label="Open link" />
        </q-td>
      </template>

      <!-- Actions Column (เหมือนเดิม) -->
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="view-icon" icon="article" @click="onClickPreview(row)" />
          </div>
        </q-td>
      </template>
    </q-table>

    <ConfirmDialog
      v-model="confirmDialogVisible"
      :title="titleDialog"
      @confirm="onConfirmDelete"
      @cancel="onCancelDelete"
    />
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar, Loading } from 'quasar';
import { quizUserColumns } from 'src/data/table_columns';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { Assessment } from 'src/types/models';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import type { QTableProps } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';

const globalStore = useGlobalStore();
const router = useRouter();
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const search = ref<string>('');
const rows = ref<Assessment[]>([]);
const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Assessment | null>(null);
const titleDialog = ref('');

async function onSearchUpdate(keyword: string) {
  const res = await new AssessmentService('quiz').fetchAllStUser(pagination.value, keyword);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
}

const { notify } = useQuasar();

const fetchDataRow = async (_pag: QTableProps['pagination']) => {
  const res = await new AssessmentService('quiz').fetchAllStUser(_pag, search.value);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
};

const handleRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  if (_pag) {
    pagination.value = _pag;
  }
  fetchDataRow(_pag).catch((error) => {
    console.error('Failed to fetch assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
};
async function onClickPreview(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    await router.push({
      name: 'quiz-preview',
      params: { linkUrl: row.linkURL.toString() }, // ✅ ใช้ linkUrl param
    });
  } catch (error) {
    console.error('Navigation to preview failed:', error);
  }
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService('quiz').deleteOne(selectedRowToDelete.value.id);
    await fetchDataRow(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

onMounted(() => {
  fetchDataRow(defaultPaginationValue).catch((error) => {
    console.error('Failed to fetch initial assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
});
</script>
<style scoped lang="scss">
// Component-specific styles only
// Most table styles are now in app.scss for consistency

/* Custom font sizes for QuizUserManagementPage */
:deep(.q-table thead th) {
  font-size: 0.95rem !important;
  padding: 8px;
}

:deep(.q-table tbody td) {
  font-size: 0.9rem !important;
  padding: 8px;
  line-height: 1.4;
}

/* Custom column widths for QuizUserManagementPage */
:deep(.q-table) {
  th,
  td {
    padding: 8px 12px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* รหัส */
  th:nth-child(1),
  td:nth-child(1) {
    width: 25px !important;
    min-width: 25px !important;
    max-width: 25px !important;
    padding-right: 4px !important;
  }

  /* ชื่อแบบทดสอบ */
  th:nth-child(2),
  td:nth-child(2) {
    min-width: 180px !important;
    width: auto !important;
  }

  /* วันที่ */
  th:nth-child(3),
  td:nth-child(3) {
    width: 140px !important;
    min-width: 140px !important;
    max-width: 140px !important;
  }

  /* เครื่องมือ */
  th:nth-child(4),
  td:nth-child(4) {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
    padding: 4px !important;
  }
}
</style>
