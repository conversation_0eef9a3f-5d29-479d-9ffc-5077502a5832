<template>
  <q-page class="q-pa-md page-wrapper">
    <div class="form-container">
      <div v-if="editFormData">
        <div
          v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
            (item) => item.section === currentSection,
          )"
          :key="index"
        >
          <UserTextBlock
            v-if="evaluateItem.headerBody && evaluateItem.type === 'HEADER'"
            :item="evaluateItem"
            :title="evaluateItem.headerBody.title || 'ไม่มีข้อมูล'"
            :description="evaluateItem.headerBody.description || 'ไม่มีข้อมูล'"
          />
          <UserQuestionBlock
            ref="questionRef"
            v-if="evaluateItem.type !== 'IMAGE' && evaluateItem.type !== 'NEXTSECTION'"
            :id="evaluateItem.id"
            :draftId="submission?.id || 0"
            :item="editFormData"
            :category="evaluateItem.type"
            :section="evaluateItem.section"
            :status="isPreview"
            :clear="isClear"
            @update-answer="handleAnswer"
            @radio-selected="isNextSection"
          />
          <UserImageBlock
            v-if="evaluateItem.type === 'IMAGE'"
            :title="evaluateItem.imageBody?.imageText || ''"
            :image-url="evaluateItem.imageBody?.imagePath || ''"
            @update:title="evaluateItem.headerBody && (evaluateItem.headerBody.title = $event)"
            @update:image-url="
              evaluateItem.imageBody && (evaluateItem.imageBody.imagePath = $event)
            "
            aria-label="dsda"
          />
        </div>
      </div>

      <div class="row q-pa-md q-gutter-sm btn-footer items-center justify-between">
        <div class="col-auto btn-left">
          <q-btn
            v-if="!isPreview"
            label="ล้างแบบสอบถาม"
            class="btn-clear"
            @click="clearForm"
            size="md"
          />
        </div>

        <div class="col-auto btn-right row q-gutter-sm">
          <q-btn
            v-if="currentSection > 1"
            label="กลับไปหน้าก่อน"
            color="secondary"
            class="text-white"
            @click="previousSection"
            style="background-color: yellow; max-width: 130px"
            size="md"
          />
          <q-btn
            v-show="hideButton"
            label="หน้าต่อไป"
            @click="BtnNextSection"
            color="primary"
            text-color="black"
            style="max-width: 130px"
            size="md"
          />
          <q-btn
            v-if="!hideButton && isPreview === false"
            label="ส่งแบบสอบถาม"
            @click="confirm"
            color="positive"
            class="text-white"
            style="max-width: 130px"
            size="md"
          />
        </div>
      </div>

      <SubmitDialog
        v-model="confirmDialog"
        :submitId="submission?.id ?? 0"
        :role="authStore.currentRoleName"
      />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserTextBlock from 'src/components/evaluate/UserEvaluateBlock/UserTextBlock.vue';
import UserImageBlock from 'src/components/evaluate/UserEvaluateBlock/UserImageBlock.vue';
import UserQuestionBlock from 'src/components/evaluate/UserEvaluateBlock/UserQuestionBlock.vue';
import { onMounted, ref, watchEffect } from 'vue';
import type { ComponentPublicInstance } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { summaryService } from 'src/services/asm/submissionService'; // หรือ path ที่คุณวางไฟล์จริงๆ
import type { Assessment, Submission, User } from 'src/types/models';
import { useRoute } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import router from 'src/router';
import { onBeforeRouteUpdate } from 'vue-router';
import SubmitDialog from 'src/components/common/SubmitDialog.vue';
import { useQuasar } from 'quasar';
const questionRef = ref<ComponentPublicInstance<typeof UserQuestionBlock>[]>([]);
const $q = useQuasar();

const errorMessage = ref('');
const nextSection = ref(0);

onMounted(async () => {
  // ป้องกัน NaN
  const sectionParam = Number(route.params.section);
  if (!isNaN(sectionParam)) {
    currentSection.value = sectionParam;
  }
  nextSection.value = 0;
  onSectionComplete(1); // ← เพิ่มบรรทัดนี้
  localStorage.setItem('currentSection', '1');
  if (currentSection.value === 1) {
    localStorage.setItem('visitedSections', JSON.stringify([1]));
    localStorage.removeItem('currentSection');
  }
  // ตรวจสอบ id ก่อนใช้
  const asmId = Number(sessionStorage.getItem('evaluate-id'));
  const url = route.params.url;
  if (route.meta.status === 'do' && asmId && url) {
    isPreview.value = false;
    user.value = authStore.getCurrentUser();

    const res = await new AssessmentService('evaluate').getAssessmentByUUID(String(url));
    editFormData.value = {
      ...res.data,
      itemBlocks:
        res.data.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };

    if (route.name === 'evaluate-do' && user.value) {
      const sub = await summaryService.getDraft(asmId, user.value?.id);
      submission.value = sub;
      if (!submission.value) {
        await summaryService.create({
          id: 0,
          assessmentId: asmId,
          userId: user.value?.id,
          startAt: '',
          endAt: '',
        });

        submission.value = await summaryService.getDraft(asmId, user.value?.id);
      }
      const checkSection =
        previous.value === true && currentSection.value !== 1
          ? currentSection.value - 1
          : currentSection.value + 1;

      checkItem.value = {
        ...res.data,
        itemBlocks: res.data.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
      };
      if (editFormData.value.itemBlocks) {
        const nextSectionItem = editFormData.value.itemBlocks.find(
          (item) => item.type === 'NEXTSECTION',
        );
        if (nextSectionItem) {
          const option = nextSectionItem?.options;
          if (option) {
            const childSection = option[0]?.nextSection;
            if (childSection) {
              nextSection.value = childSection ?? 0;
            }
          }
        } else {
          return;
        }
      }
      hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
    }
  } else if (route.meta.status === 'preview' && route.params.id) {
    isPreview.value = true;
    const sectionParam = Number(route.params.section);
    if (!isNaN(sectionParam)) {
      currentSection.value = sectionParam;
    }
    const asmId = Number(route.params.id);
    const { assessment, pagedItemBlocks } = await new AssessmentService('evaluate').fetchOne(asmId);

    editFormData.value = {
      ...assessment,
      itemBlocks: pagedItemBlocks.filter((item) => item.section === currentSection.value),
    };
    const checkSection =
      previous.value === true && currentSection.value !== 1
        ? currentSection.value - 1
        : currentSection.value + 1;

    checkItem.value = {
      ...assessment,
      itemBlocks: pagedItemBlocks.filter((item) => item.section === checkSection),
    };

    hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
  }
});

onBeforeRouteUpdate(async (to) => {
  const sectionParam = Number(to.params.section);
  if (!isNaN(sectionParam)) {
    currentSection.value = sectionParam;
  }
  onSectionComplete(1); // ← เพิ่มบรรทัดนี้
  localStorage.setItem('currentSection', '1');
  if (currentSection.value === 1) {
    nextSection.value = 0;
    localStorage.setItem('visitedSections', JSON.stringify([1]));
    localStorage.removeItem('currentSection');
  }

  if (to.meta.status === 'do') {
    const url = String(to.params.url);
    user.value = authStore.getCurrentUser();

    const res = await new AssessmentService('evaluate').getAssessmentByUUID(url);

    editFormData.value = {
      ...res.data,
      itemBlocks:
        res.data.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };
    const checkSection =
      previous.value === true && currentSection.value !== 1
        ? currentSection.value - 1
        : currentSection.value + 1;

    checkItem.value = {
      ...res.data,
      itemBlocks: res.data.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
    };
    if (editFormData.value.itemBlocks && checkItem.value.itemBlocks?.length) {
      const nextSectionItem = editFormData.value.itemBlocks.find(
        (item) => item.type === 'NEXTSECTION',
      );
      if (nextSectionItem) {
        const option = nextSectionItem?.options;
        if (option) {
          const childSection = option[0]?.nextSection;
          if (childSection) {
            nextSection.value = childSection ?? 0;
          }
        }
      } else {
        return;
      }
    } else {
      nextSection.value = 0;
    }
    if (nextSection.value !== 0) {
      checkItem.value = {
        ...res.data,
        itemBlocks: res.data.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
      };
    } else {
      hideButton.value = false;
    }
  } else if (to.meta.status === 'preview' && route.params.id) {
    isPreview.value = true;
    const asmId = Number(route.params.id);
    const { assessment, pagedItemBlocks } = await new AssessmentService('evaluate').fetchOne(asmId);

    editFormData.value = {
      ...assessment,
      itemBlocks: pagedItemBlocks.filter((item) => item.section === currentSection.value),
    };

    const checkSection =
      previous.value === true && currentSection.value !== 1
        ? currentSection.value - 1
        : currentSection.value + 1;

    checkItem.value = {
      ...assessment,
      itemBlocks: pagedItemBlocks.filter((item) => item.section === checkSection),
    };

    hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
  }
});

function onSectionComplete(sectionNumber: number) {
  const visitedRaw = localStorage.getItem('visitedSections');
  const visited = visitedRaw ? JSON.parse(visitedRaw) : [];

  if (!visited.includes(sectionNumber)) {
    visited.push(sectionNumber);
    localStorage.setItem('visitedSections', JSON.stringify(visited));
  }
}
const route = useRoute();
const checkItem = ref<Assessment>();
const editFormData = ref<Assessment>();
const isPreview = ref(false);
const evaluateId = ref(0);
const currentSection = ref(1);
const previous = ref(false);
//Submittion
const submission = ref<Submission>();
const user = ref<User>();
const authStore = useAuthStore();
const isClear = ref(false);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const answers = ref<{ [key: string]: any }>({});

type AnswerValue = string | string[] | Record<string, number> | File[] | null;

const handleAnswer = (payload: { id: string | number; value: AnswerValue }) => {
  answers.value[payload.id] = payload.value;
};

watchEffect(() => {
  evaluateId.value = Number(route.query.id) || 0;
});

const hideButton = ref(true);

// เปลี่ยน section และโหลดข้อมูล พร้อม push query
const updateSection = async (newSection: number) => {
  currentSection.value = newSection;
  if (isPreview.value === true) {
    await router.push({
      name: 'evaluate-preview',
      params: {
        id: Number(route.params.id),
        section: currentSection.value,
      },
    });
  } else {
    await router.push({
      name: 'evaluate-do',
      params: {
        url: String(route.params.url),
        section: currentSection.value,
      },
      query: {
        id: route.query.id, // ใส่กลับไปให้เหมือนเดิม
      },
    });
  }
  isClear.value = false;
};

const checkSection = ref<number | null>(null);

const isNextSection = (section: number) => {
  if (section !== 1) {
    checkSection.value = section;
  } else {
    checkSection.value = currentSection.value;
  }
};

const BtnNextSection = async () => {
  const isValid = questionRef.value.every((ref) => ref?.validateAnswer?.());

  if (!isValid && route.meta.status === 'do') {
    errorMessage.value = 'กรุณาตอบคำถามข้อที่มีเครื่องหมาย * ก่อนดำเนินการต่อ';
    $q.notify({
      type: 'negative',
      message: errorMessage.value,
      position: 'bottom',
    });
    return;
  } else {
    previous.value = false;
    console.log(checkSection.value, 'click next');
    if (checkSection.value !== 1 && checkSection.value) {
      localStorage.setItem('currentSection', String(currentSection.value));
      currentSection.value = checkSection.value;
      console.log(nextSection.value, 'correct');
      if (nextSection.value !== 0) {
        await updateSection(nextSection.value);
        onSectionComplete(currentSection.value);
      } else {
        console.log('correct');
        await updateSection(currentSection.value);
        onSectionComplete(currentSection.value);
      }
    } else {
      if (nextSection.value !== 0) {
        await updateSection(nextSection.value);
        onSectionComplete(currentSection.value);
      } else {
        await updateSection(currentSection.value + 1);
        onSectionComplete(currentSection.value + 1);
      }
    }
  }
};

const previousSection = async () => {
  const visitedRaw = localStorage.getItem('visitedSections');
  const visited: number[] = visitedRaw ? JSON.parse(visitedRaw) : [];

  const currentIndex = visited.indexOf(currentSection.value);
  if (currentIndex > 0) {
    const prevSection = visited[currentIndex - 1];
    if (typeof prevSection === 'number') {
      previous.value = true;
      await updateSection(prevSection);
      currentSection.value = prevSection;
      localStorage.setItem('currentSection', String(prevSection));
    }
  }
  hideButton.value = true;
};
const clearForm = () => {
  isClear.value = true;
};

const confirmDialog = ref(false);
const confirm = () => {
  const isValid = questionRef.value.every((ref) => ref?.validateAnswer?.());

  if (!isValid) {
    errorMessage.value = 'กรุณาตอบคำถามข้อที่มีเครื่องหมาย * ก่อนดำเนินการต่อ';
    $q.notify({
      type: 'negative',
      message: errorMessage.value,
      position: 'bottom',
    });
  } else {
    confirmDialog.value = true;
  }
};
</script>

<style scoped lang="scss">
.btn-footer {
  max-width: 900px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.btn-left {
  flex-grow: 1; /* ดันให้อยู่ซ้ายสุด */
}

.btn-right {
  justify-content: flex-end;
  display: flex;
  gap: 8px; /* ช่องว่างระหว่างปุ่ม */
}

.full-width {
  width: 100%;
}
.btn-clear {
  background-color: white;
  color: $primary;
  max-width: 130px;
}
.page-wrapper {
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}
</style>
