<template>
  <q-page class="q-pa-md">
    <div class="q-mb-lg">
      <h4 class="text-h4 q-mb-md">ThreeDots Component Demo</h4>
      <p class="text-body1 text-grey-7">
        A reusable component that displays three dots horizontally using Quasar Framework standards.
      </p>
    </div>

    <!-- Basic Examples -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <h5 class="text-h5 q-mb-md">Basic Examples</h5>

        <div class="q-mb-md">
          <h6 class="text-h6">Default (Small, Grey)</h6>
          <ThreeDots />
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Medium Size, Blue</h6>
          <ThreeDots size="md" color="primary" />
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Large Size, Red</h6>
          <ThreeDots size="lg" color="negative" />
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Extra Large, Green</h6>
          <ThreeDots size="xl" color="positive" />
        </div>
      </q-card-section>
    </q-card>

    <!-- Spacing Examples -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <h5 class="text-h5 q-mb-md">Spacing Variations</h5>

        <div class="q-mb-md">
          <h6 class="text-h6">Extra Small Spacing</h6>
          <ThreeDots spacing="xs" size="md" />
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Medium Spacing</h6>
          <ThreeDots spacing="md" size="md" />
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Large Spacing</h6>
          <ThreeDots spacing="lg" size="md" />
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Extra Large Spacing</h6>
          <ThreeDots spacing="xl" size="md" />
        </div>
      </q-card-section>
    </q-card>

    <!-- Layout Examples -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <h5 class="text-h5 q-mb-md">Layout Examples</h5>

        <div class="q-mb-md">
          <h6 class="text-h6">Vertical Arrangement</h6>
          <ThreeDots vertical size="md" color="accent" />
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Inline with Text</h6>
          <p class="text-body1">Loading <ThreeDots inline size="xs" /> Please wait...</p>
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">In a Card Header</h6>
          <q-card class="bg-grey-1">
            <q-card-section class="row items-center">
              <div class="text-h6">Processing</div>
              <q-space />
              <ThreeDots size="sm" color="primary" />
            </q-card-section>
          </q-card>
        </div>
      </q-card-section>
    </q-card>

    <!-- Usage Examples -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <h5 class="text-h5 q-mb-md">Common Use Cases</h5>

        <div class="q-mb-md">
          <h6 class="text-h6">Loading Indicator</h6>
          <q-card class="bg-blue-1">
            <q-card-section class="text-center">
              <div class="text-body1 q-mb-sm">Loading data...</div>
              <ThreeDots size="md" color="primary" />
            </q-card-section>
          </q-card>
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Menu Separator</h6>
          <div class="row items-center q-gutter-md">
            <q-btn flat label="Home" />
            <ThreeDots size="xs" color="grey-5" />
            <q-btn flat label="About" />
            <ThreeDots size="xs" color="grey-5" />
            <q-btn flat label="Contact" />
          </div>
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Status Indicator</h6>
          <q-list>
            <q-item>
              <q-item-section>
                <q-item-label>Task 1</q-item-label>
              </q-item-section>
              <q-item-section side>
                <ThreeDots size="sm" color="positive" />
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>Task 2</q-item-label>
              </q-item-section>
              <q-item-section side>
                <ThreeDots size="sm" color="warning" />
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-card-section>
    </q-card>

    <!-- Code Examples -->
    <q-card>
      <q-card-section>
        <h5 class="text-h5 q-mb-md">Usage Examples</h5>

        <div class="q-mb-md">
          <h6 class="text-h6">Basic Usage</h6>
          <q-code> &lt;ThreeDots /&gt; </q-code>
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">With Props</h6>
          <q-code> &lt;ThreeDots size="lg" color="primary" spacing="md" /&gt; </q-code>
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Vertical Layout</h6>
          <q-code> &lt;ThreeDots vertical size="md" color="accent" /&gt; </q-code>
        </div>

        <div class="q-mb-md">
          <h6 class="text-h6">Inline Usage</h6>
          <q-code> Loading &lt;ThreeDots inline size="xs" /&gt; Please wait... </q-code>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts"></script>

<style scoped>
.q-code {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  border: 1px solid #e0e0e0;
}
</style>
