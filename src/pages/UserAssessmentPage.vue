<template>
  <q-page padding>
    <TopHeaderTable :title="pageTitle" :show-create-button="false" @search="onSearchUpdate" />

    <q-table
      :rows="rows"
      :columns="assessmentColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      v-model:pagination="pagination"
      @request="handleRequest"
      binary-state-sort
      class="no-scroll"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="view-icon" icon="article" @click="onClickPreview(row)" />
          </div>
        </q-td>
      </template>
    </q-table>
    <ConfirmDialog
      v-model="confirmDialogVisible"
      :title="titleDialog"
      @confirm="onConfirmDelete"
      @cancel="onCancelDelete"
    />
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { quizUserColumns } from 'src/data/table_columns';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { Assessment } from 'src/types/models';
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import type { QTableProps } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';

const globalStore = useGlobalStore();
const router = useRouter();
const route = useRoute();
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const search = ref<string>('');
const rows = ref<Assessment[]>([]);
const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Assessment | null>(null);
const titleDialog = ref('');

const props = defineProps<{ type?: 'quiz' | 'evaluate' }>();

const type = computed<'quiz' | 'evaluate'>(() => {
  const t = props.type || route.params.type || 'quiz';
  return t === 'evaluate' ? 'evaluate' : 'quiz';
});
const pageTitle = computed(() =>
  type.value === 'evaluate' ? 'แบบสอบถามทั้งหมด' : 'แบบทดสอบทั้งหมด',
);
const assessmentColumns = computed(() => quizUserColumns); // You can switch columns if needed

async function onSearchUpdate(keyword: string) {
  const res = await new AssessmentService(type.value).fetchAllStUser(pagination.value, keyword);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
}

const { notify } = useQuasar();

const fetchDataRow = async (_pag: QTableProps['pagination']) => {
  const res = await new AssessmentService(type.value).fetchAllStUser(_pag, search.value);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
};

const handleRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  if (_pag) {
    pagination.value = _pag;
  }
  fetchDataRow(_pag).catch((error) => {
    console.error('Failed to fetch assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูล',
    });
  });
};
async function onClickPreview(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    if (type.value === 'evaluate') {
      const res = await new AssessmentService('evaluate').getAssessmentByUUID(row.linkURL);
      console.log('eiei');
      sessionStorage.setItem('evaluate-id', res.data.id.toString());

      await router.push({
        name: 'evaluate-do',
        params: { url: res.data.linkURL, section: 1 },
      });
    } else {
      await router.push({
        name: 'quiz-do',
        params: { linkUrl: row.linkURL.toString() },
      });
    }
  } catch (error) {
    console.error('Navigation to do page failed:', error);
  }
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService(type.value).deleteOne(selectedRowToDelete.value.id);
    await fetchDataRow(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

watch(
  () => type.value,
  async () => {
    await fetchDataRow(defaultPaginationValue).catch((error) => {
      console.error('Failed to fetch assessments:', error);
      notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการดึงข้อมูล',
      });
    });
  },
  { immediate: true },
);

onMounted(() => {
  fetchDataRow(defaultPaginationValue).catch((error) => {
    console.error('Failed to fetch initial assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูล',
    });
  });
  localStorage.removeItem('visitedSections');
});
</script>
<style scoped lang="scss">
// Component-specific styles only
// Most table styles are now in app.scss for consistency

/* Custom column widths for UserAssessmentPage */
:deep(.q-table) th:nth-child(1),
:deep(.q-table) td:nth-child(1) {
  width: 6.11% !important;
}

:deep(.q-table) th:nth-child(2),
:deep(.q-table) td:nth-child(2) {
  width: 25% !important;
}

:deep(.q-table) th:nth-child(3),
:deep(.q-table) td:nth-child(3) {
  width: 15% !important;
}

:deep(.q-table) th:nth-child(4),
:deep(.q-table) td:nth-child(4) {
  width: 20% !important;
}

/* Responsive Design */
@media screen and (max-width: 600px) {
  :deep(.q-table) {
    font-size: 0.9rem;
  }

  :deep(.q-table thead th) {
    font-size: 0.95rem;
    padding: 8px 4px;
  }

  :deep(.q-table tbody td) {
    font-size: 0.9rem;
    padding: 8px 4px;
  }

  .view-icon,
  .edit-graph-icon,
  .del-icon {
    padding: 4px;
  }
}

/* Table Animation */
:deep(.q-table tbody tr) {
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

@for $i from 1 through 10 {
  :deep(.q-table tbody tr:nth-child(#{$i})) {
    animation-delay: #{$i * 0.05}s;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
