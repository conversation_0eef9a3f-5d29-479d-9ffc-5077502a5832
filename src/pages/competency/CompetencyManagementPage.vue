<template>
  <q-page padding class="q-gutter-y-md">
    <!-- <TopHeaderTable
      title="จัดการสมรรถนะ"
      create-button-label="เพิ่มสมรรถนะ"
      @search="onSearchUpdate"
      @create="onClickAdd"
    /> -->

    <TopHeaderTable
      title="จัดการสมรรถนะ"
      create-button-label="เพิ่ม"
      pageType="competency"
      @search="onSearchUpdate"
      @create="onClickAdd"
      :show-create="showCreateButton"
      subtitle="สมรรถนะหลัก"
    >
      <template #tab>
        <TabNavigation class="q-mt-sm" :tabs="tabItems" v-model="selectedTab" />
      </template>
    </TopHeaderTable>

    <q-table
      :rows="rows"
      :columns="competencyManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <template v-if="selectedTab !== 'core'">
              <q-btn dense unelevated class="view-icon" icon="edit" @click="onClickEdit(row)" />
              <q-btn dense unelevated class="view-icon" icon="delete" @click="onClickDelete(row)" />
            </template>
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { defineAsyncComponent, ref, watch, onMounted, computed } from 'vue';
import { competencyManagementColumns } from 'src/data/table_columns';
import type { Competency } from 'src/types/models';

// import components
import TopHeaderTable from 'src/components/common/TopHeaderTable.vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';

const $q = useQuasar();

// ---------------------------
// Fixed core competencies
const coreCompetencies: Competency[] = [
  {
    id: 1,
    name: 'การมุ่งผลสัมฤทธิ์',
    description: 'ความมุ่งมั่นในการปฏิบัติงานให้มีคุณภาพ',
    comp_type: 'Core Competency',
    career_type: 'core',
  },
  {
    id: 2,
    name: 'การบริการที่ดี',
    description: 'ความตั้งใจในการให้บริการที่ดีแก่ผู้รับบริการ',
    comp_type: 'Core Competency',
    career_type: 'core',
  },
  {
    id: 3,
    name: 'การสั่งสมความเชี่ยวชาญ',
    description: 'ความสนใจใฝ่รู้ สั่งสมความรู้และความเชี่ยวชาญ',
    comp_type: 'Core Competency',
    career_type: 'core',
  },
  {
    id: 4,
    name: 'จริยธรรม',
    description: 'การครองตนและประพฤติปฏิบัติถูกต้องเหมาะสม',
    comp_type: 'Core Competency',
    career_type: 'core',
  },
  {
    id: 5,
    name: 'การทำงานเป็นทีม',
    description: 'ความตั้งใจที่จะทำงานร่วมกับผู้อื่น',
    comp_type: 'Core Competency',
    career_type: 'core',
  },
];

// Other competencies that can be modified
const mockCompetencies: Competency[] = [
  ...coreCompetencies,
  {
    id: 6,
    name: 'การสอนและถ่ายทอดความรู้',
    description: 'ความสามารถในการสอนและถ่ายทอดความรู้',
    comp_type: 'Academic Competency',
    career_type: 'academic',
  },
  {
    id: 7,
    name: 'การวิจัยและพัฒนา',
    description: 'ความสามารถในการทำวิจัยและพัฒนา',
    comp_type: 'Academic Competency',
    career_type: 'academic',
  },
  {
    id: 8,
    name: 'การบริหารจัดการ',
    description: 'ความสามารถในการบริหารจัดการงาน',
    comp_type: 'Support Competency',
    career_type: 'support',
  },
  {
    id: 9,
    name: 'ภาวะผู้นำ',
    description: 'ความสามารถในการเป็นผู้นำและบริหารทีม',
    comp_type: 'Management Competency',
    career_type: 'admin',
  },
];

// ---------------------------
// Tab Setup
const tabItems = [
  { label: 'สมรรถนะหลัก', value: 'core' },
  { label: 'สมรรถนะสายวิชาการ', value: 'academic' },
  { label: 'สมรรถนะสายสนับสนุนวิชาการ', value: 'support' },
  { label: 'สมรรถนะทางการบริหาร', value: 'admin' },
];

const TAB_KEY = 'selected-competency-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'core');
const filteredRowsByTab = ref<Competency[]>([]);
const searchKeyword = ref('');

// ---------------------------
// Filtering logic
const filterCompetenciesByTab = (tabValue: string) => {
  filteredRowsByTab.value = mockCompetencies.filter((item) => item.career_type === tabValue);
};

watch(selectedTab, (val) => {
  localStorage.setItem(TAB_KEY, val);
  filterCompetenciesByTab(val);
});

    // Run on first mount
onMounted(() => {
  filterCompetenciesByTab(selectedTab.value);
});

// ---------------------------
// Hide create button for core competencies
const showCreateButton = computed(() => selectedTab.value !== 'core');

// ---------------------------
// Computed rows with search
const rows = computed(() => {
  if (!searchKeyword.value) return filteredRowsByTab.value;
  const keyword = searchKeyword.value.toLowerCase();
  return filteredRowsByTab.value.filter(
    (item) =>
      item.name?.toLowerCase().includes(keyword) ||
      item.description?.toLowerCase().includes(keyword) ||
      item.comp_type?.toLowerCase().includes(keyword),
  );
});// ---------------------------
// Actions
const onClickEdit = (row: Competency) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/competency/CompetencyForm.vue')),
    componentProps: { title: 'แก้ไขสมรรถนะ', formData: row },
    persistent: true,
  }).onOk((data: Competency) => {
    const index = mockCompetencies.findIndex((item) => item.id === data.id);
    if (index !== -1) {
      mockCompetencies[index] = { ...data };
      filterCompetenciesByTab(selectedTab.value);
    }
  });
};

const onClickAdd = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/competency/CompetencyForm.vue')),
    componentProps: { title: 'สร้างสมรรถนะใหม่' },
    persistent: true,
  }).onOk((data: Competency) => {
    const newId = Math.max(...mockCompetencies.map((r) => r.id)) + 1;
    mockCompetencies.push({ ...data, id: newId });
    filterCompetenciesByTab(selectedTab.value);
  });
};

const onClickDelete = (row: Competency) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณต้องการลบสมรรถนะ "${row.name}" ใช่หรือไม่?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const index = mockCompetencies.findIndex((item) => item.id === row.id);
    if (index !== -1) {
      mockCompetencies.splice(index, 1);
      filterCompetenciesByTab(selectedTab.value);
    }
  });
};

const onSearchUpdate = (keyword: string) => {
  searchKeyword.value = keyword;
};
</script>

<style scoped></style>
