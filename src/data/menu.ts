import type { QTabProps } from 'quasar';
import type { MenuLink } from 'src/types/app';

export const allDrawerMenu: MenuLink[] = [
  {
    title: 'หน้าหลัก',
    link: '/home',
    icon: 'home',
  },
  {
    title: 'Monitor',
    link: '/monitor',
    icon: 'home',
  },
  {
    title: 'จัดการผู้ใช้งาน',
    link: '/ums/management',
    icon: 'group',
  },
  {
    title: 'จัดการแบบทดสอบ',
    link: '/quiz/management',
    icon: 'quiz',
  },
  {
    title: 'จัดการแบบประเมิน',
    link: '/evaluate/management',
    icon: 'library_books',
  },
  {
    title: 'ทำแบบทดสอบ',
    link: '/quiz/user',
    icon: 'app:test-quiz',
  },
  {
    title: 'ทำแบบประเมิน',
    link: '/evaluate/user',
    icon: 'mdi-application-edit',
  },
  {
    title: 'สมรรถนะ',
    link: '/competency/management',
    icon: 'mdi-brain',
  },
  {
    title: 'ทักษะ',
    link: '/skill/management',
    icon: 'code',
  },
  {
    title: 'IDP',
    link: '/idp/management',
    icon: 'mdi-account-tie',
  },
  {
    title: 'จัดการอายุการปฏิบัติงาน',
    link: '/idp/age-work-criteria',
    icon: 'support_agent',
  },
  {
    title: 'In House Training',
    link: '/in-house/management',
    icon: 'mdi-bag-personal',
  },
  {
    title: 'จัดการแผนพัฒนา',
    link: '/developments/management',
    icon: 'mdi-book-edit',
  },
  {
    title: 'แผนพัฒนาของฉัน',
    link: '/developments/my-plan',
    icon: 'mdi-star',
  },
];

export const managerMenu: MenuLink[] = [
  {
    title: 'หน้าหลัก',
    link: '/home',
    icon: 'home',
  },
  {
    title: 'จัดการแบบทดสอบ',
    link: '/quiz/management',
    icon: 'quiz',
  },
  {
    title: 'จัดการแบบประเมิน',
    link: '/evaluate/management',
    icon: 'library_books',
  },
];

export const standardUserMenu: MenuLink[] = [
  {
    title: 'หน้าหลัก',
    link: '/home',
    icon: 'home',
  },
  {
    title: 'ทำแบบทดสอบ',
    link: '/quiz/user',
    icon: 'app:test-quiz',
  },
  {
    title: 'ทำแบบประเมิน',
    link: '/evaluate/user',
    icon: 'mdi-application-edit',
  },
];

export const LogOutMenu: MenuLink[] = [
  {
    title: 'ออกจากระบบ',
    link: 'login',
    icon: 'logout',
  },
];

// * maybe used in future for more complex menu structures
// export const adminDrawerMenu: MenuLink[] = [
//   {
//     title: 'หน้าหลัก',
//     link: '/home',
//     icon: 'home',
//   },
//   {
//     title: 'ออกจากระบบ',
//     link: '/logout',
//     icon: 'logout',
//   },
// ];

export const defaultAsmTabsMenu: QTabProps[] = [
  {
    label: 'คำถาม',
    name: 'questions',
    icon: 'help',
  },
  {
    label: 'การตอบ',
    name: 'replies',
    icon: 'reply',
  },
  {
    label: 'ตั้งค่า',
    name: 'settings',
    icon: 'settings',
  },
];

export const defaultUmsTabsMenu: QTabProps[] = [
  {
    label: 'บทบาท',
    name: 'roles',
  },
  {
    label: 'ผู้ใช้งาน',
    name: 'users',
  },
];
