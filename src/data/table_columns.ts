import type { QTableColumn } from 'quasar';
import type { Assessment, Permission, User } from 'src/types/models';
import { formatDateDisplay } from 'src/utils/utils';

export const quizManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left', field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแบบสอบถาม',
    align: 'left',
    field: 'name',
    sortable: true,
    style: 'min-width: 250px; white-space: normal;',
  },
  {
    name: 'creator',
    label: 'ผู้สร้าง',
    align: 'left',
    field: (row) => row.creator?.name || '-',
    sort: (a, b) => (a.creator?.name || '').localeCompare(b.creator?.name || ''),
    sortable: true,
    style: 'min-width: 150px;',
  },
  {
    name: 'startAt',
    label: 'ช่วงเวลาเปิด - ปิด',
    align: 'left' as const,
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
    style: 'min-width: 180px;',
  },
  {
    name: 'link',
    label: 'ลิงก์',
    align: 'center' as const,
    field: 'assessmentLink',
    sortable: false,
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center' as const,
    field: () => '',
    sortable: false,
    style: 'min-width: 180px;',
  },
];

export const quizUserColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left', field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแบบสอบถาม',
    align: 'left',
    field: 'name',
    sortable: true,
    style: 'min-width: 250px; white-space: normal;',
  },
  {
    name: 'startAt',
    label: 'เวลาเปิด - ปิด',
    align: 'left' as const,
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
    style: 'min-width: 180px;',
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center' as const,
    field: () => '',
    sortable: false,
    style: 'min-width: 180px;',
  },
];

export const evaluateManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left', field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแบบสอบถาม',
    align: 'left',
    field: 'name',
    sortable: true,
    style: 'min-width: 250px; white-space: normal;',
  },
  {
    name: 'creator',
    label: 'ผู้สร้าง',
    align: 'left',
    field: (row) => row.creator?.name || '-',
    sort: (a, b) => (a.creator?.name || '').localeCompare(b.creator?.name || ''),
    sortable: true,
    style: 'min-width: 150px;',
  },
  {
    name: 'startAt',
    label: 'ช่วงเวลาเปิด - ปิด',
    align: 'left',
    field: (row: Assessment) =>
      `${formatDateDisplay(row.startAt)} - ${formatDateDisplay(row.endAt)}`,
    sortable: true,
    style: 'min-width: 180px;',
  },
  {
    name: 'link',
    label: 'ลิงก์',
    align: 'center',
    field: (rows: { link: string }) => rows.link ?? '-',
    sortable: false,
  },
  {
    name: 'actions',
    label: 'เครื่องมือ',
    align: 'center',
    field: () => '',
    sortable: false,
    style: 'min-width: 180px;',
  },
];

export const userColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'ID',
    align: 'left',
    field: (row: User) => row.id,
    sortable: true,
  },
  {
    name: 'name',
    align: 'left',
    label: 'ชื่อ-นามสกุล',
    field: 'name',
  },
  { name: 'username', align: 'left', label: 'ชื่อผู้ใช้งาน', field: 'email' },
  {
    name: 'role',
    align: 'left',
    label: 'บทบาท',
    field: (row: User) => row.roles?.map((role) => role.name).join(', ') || '-',
  },
  { name: 'actions', align: 'center', label: 'เครื่องมือ', field: '' },
];

export const roleColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'ID',
    align: 'left',
    field: (row: User) => row.id,
    sortable: true,
  },
  {
    name: 'name',
    align: 'left',
    label: 'ชื่อ',
    field: 'name',
  },
  {
    name: 'description',
    align: 'left',
    label: 'รายละเอียด',
    field: 'description',
  },
  { name: 'actions', align: 'center', label: 'เครื่องมือ', field: '' },
];

export const permColumns = <QTableColumn[]>[
  {
    name: 'id',
    required: true,
    label: 'ID',
    align: 'left',
    field: (row: User) => row.id,
    sortable: true,
  },
  {
    name: 'name',
    align: 'left',
    label: 'ชื่อ',
    field: 'name',
  },
  {
    name: 'nameEn',
    align: 'left',
    label: 'ชื่ออังกฤษ',
    field: 'nameEn',
  },
  {
    name: 'status',
    align: 'left',
    label: 'สถานะ',
    field: (row: Permission) => (row.status ? 'ใช้งาน' : 'ไม่ใช้งาน'),
  },
  {
    name: 'default',
    align: 'left',
    label: 'ค่าเริ่มต้น',
    field: (row: Permission) => (row.isDefault ? 'ใช่' : 'ไม่ใช่'),
  },
  { name: 'actions', align: 'center', label: 'เครื่องมือ', field: '' },
];

export const competencyManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left' as const, field: 'id', sortable: true },
  {
    name: 'competency',
    label: 'ชื่อสมรรถนะ',
    align: 'left' as const,
    field: 'name',
    sortable: true,
    style: 'min-width: 200px; white-space: normal;',
  },
  {
    name: 'description',
    label: 'รายละเอียด',
    align: 'left' as const,
    field: 'description',
    sortable: true,
    style: 'min-width: 300px; white-space: normal;',
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: '', sortable: false },
];

export const skillManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'left' as const, field: 'id', sortable: true },
  {
    name: 'skill',
    label: 'ชื่อความรู้และทักษะ',
    align: 'left' as const,
    field: 'name',
    sortable: true,
    style: 'min-width: 200px; white-space: normal;',
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: '', sortable: false },
];

export const IDP_DEVELOPMENT_PLANSManagementColumns = <QTableColumn[]>[
  { name: 'id', label: 'รหัส', align: 'center' as const, field: 'id', sortable: true },
  {
    name: 'name',
    label: 'ชื่อแผนพัฒนา',
    align: 'center' as const,
    field: 'name',
    sortable: true,
    style: 'text-align: left;',
  },
  {
    name: 'is_active',
    label: 'สถานะ',
    align: 'center' as const,
    field: 'is_active',
    sortable: true,
  },
  { name: 'actions', label: 'เครื่องมือ', align: 'center' as const, field: '', sortable: false },
];
