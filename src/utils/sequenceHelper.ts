import type { ItemBlock } from 'src/types/models';

// อัปเดตลำดับ sequence ของ block ให้เรียงตาม index
export function updateBlockSequences(blocks: ItemBlock[]): void {
  blocks.forEach((block, index) => {
    block.sequence = index + 1;
  });
}
export function getAllowedSectionsForUser(): number[] {
  const maxSectionDone = Number(localStorage.getItem('maxSectionDone')) || 1;
  return Array.from({ length: maxSectionDone }, (_, i) => i + 1);
}

// อัปเดตหมายเลข section ของ block หลังจากลบ header block
export function updateSectionNumbers(
  blocks: ItemBlock[],
  deletedSectionNumber: number,
): {
  updatedBlocks: ItemBlock[];
  sectionMap: Map<number, number>;
} {
  // ลบ block ที่อยู่ใน section ที่ถูกลบ
  const remainingBlocks = blocks.filter((block) => block.section !== deletedSectionNumber);

  // หา header block และเรียงตาม section
  const headerBlocks = remainingBlocks
    .filter((block) => block.type === 'HEADER')
    .sort((a, b) => a.section - b.section);

  // สร้าง mapping ของ section เดิม -> section ใหม่
  const sectionMap = new Map<number, number>();
  headerBlocks.forEach((header, idx) => {
    const oldSection = header.section;
    const newSection = idx + 1;
    sectionMap.set(oldSection, newSection);
    header.section = newSection;
  });

  // อัปเดต section ของ block อื่น ๆ ตาม mapping
  const updatedBlocks: ItemBlock[] = [];
  remainingBlocks.forEach((block) => {
    if (sectionMap.has(block.section)) {
      const oldSection = block.section;
      const newSection = sectionMap.get(block.section)!;
      if (oldSection !== newSection) {
        block.section = newSection;
        updatedBlocks.push(block);
      }
    }
  });

  return {
    updatedBlocks,
    sectionMap,
  };
}

// คำนวณ sequence ถัดไปสำหรับ block ใหม่
export function calculateNextSequence(_blocks: ItemBlock[], insertIndex: number): number {
  return insertIndex + 2;
}

// อัปเดตลำดับ sequence หลังแทรก block ใหม่
export function updateSequencesAfterInsertion(
  blocks: ItemBlock[],
  insertIndex: number,
  newBlock: ItemBlock,
): void {
  // แทรก block ใหม่
  blocks.splice(insertIndex + 1, 0, newBlock);

  // อัปเดต sequence ทั้งหมด
  updateBlockSequences(blocks);
}

// อัปเดตลำดับ sequence หลังลบ block
export function updateSequencesAfterDeletion(blocks: ItemBlock[], deletedIndex: number): void {
  // ลบ block ที่ต้องการ
  blocks.splice(deletedIndex, 1);

  // อัปเดต sequence ทั้งหมด
  updateBlockSequences(blocks);
}

// หา section ที่มีหมายเลขมากที่สุดใน blocks
export function findMaxSectionNumber(blocks: ItemBlock[]): number {
  return blocks.reduce((max, block) => Math.max(max, block.section || 1), 1);
}

// ดึง block ทั้งหมดที่อยู่ใน section ที่ระบุ
export function getBlocksBySection(blocks: ItemBlock[], sectionNumber: number): ItemBlock[] {
  return blocks.filter((block) => block.section === sectionNumber);
}

// อัปเดตลำดับและ sequence ของ assessment blocks หลังแทรก block ใหม่
export function updateAssessmentBlockOrder(
  assessmentBlocks: ItemBlock[],
  newBlock: ItemBlock,
  insertIndex: number,
): ItemBlock[] {
  // แทรก block ใหม่ในตำแหน่งที่ถูกต้อง
  const newAssessmentBlocks = [
    ...assessmentBlocks.slice(0, insertIndex + 1),
    newBlock,
    ...assessmentBlocks.slice(insertIndex + 1),
  ];

  // อัปเดต sequence
  updateBlockSequences(newAssessmentBlocks);

  return newAssessmentBlocks;
}

// เรียงลำดับ blocks ใหม่ตาม newOrder และอัปเดต sequence
export function reorderBlocks(blocks: ItemBlock[], newOrder: ItemBlock[]): void {
  blocks.splice(0, blocks.length, ...newOrder);
  updateBlockSequences(blocks);
}

// ตรวจสอบความถูกต้องของลำดับ sequence
export function validateSequenceIntegrity(blocks: ItemBlock[]): {
  valid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  const sequences = blocks.map((block) => block.sequence).sort((a, b) => a - b);
  for (let i = 0; i < sequences.length; i++) {
    if (sequences[i] !== i + 1) {
      issues.push(`Expected sequence ${i + 1}, found ${sequences[i]}`);
    }
  }
  return {
    valid: issues.length === 0,
    issues,
  };
}

// แก้ไข sequence ให้ถูกต้องถ้าลำดับผิด
export function fixSequenceOrder(blocks: ItemBlock[]): {
  fixed: boolean;
  changes: number;
} {
  let changes = 0;
  blocks.forEach((block, index) => {
    const expectedSequence = index + 1;
    if (block.sequence !== expectedSequence) {
      block.sequence = expectedSequence;
      changes++;
    }
  });
  return {
    fixed: changes > 0,
    changes,
  };
}

// คัดลอก block และแทรกในตำแหน่งที่ถูกต้อง พร้อมจัดการ sequence
export function insertDuplicatedBlock(
  blocks: ItemBlock[],
  sourceIndex: number,
  duplicatedBlock: ItemBlock,
): void {
  const insertIndex = sourceIndex + 1;
  // ตรวจสอบ index และ block ต้นทาง
  const sourceBlock = blocks[sourceIndex];
  if (!sourceBlock) {
    throw new Error(`Source block at index ${sourceIndex} is undefined`);
  }
  // กำหนด sequence ให้ block ที่คัดลอก
  duplicatedBlock.sequence = sourceBlock.sequence + 1;
  // แทรก block ที่คัดลอก
  blocks.splice(insertIndex, 0, duplicatedBlock);
  // อัปเดต sequence ของ block ถัดไปทั้งหมด
  for (let i = insertIndex + 1; i < blocks.length; i++) {
    const block = blocks[i];
    if (block) {
      block.sequence = i + 1;
    }
  }
}
