import { blockBodyOptions } from 'src/data/blocks';
import type { BlockBodyOptionsType } from 'src/types/app';
import type { ItemBlock, Option } from 'src/types/models';

// ฟังก์ชันดึงประเภท block body จาก ItemBlock
export const extractBlockBodyType = (itemBlock: ItemBlock): BlockBodyOptionsType | null => {
  // Special handling for NEXTSECTION blocks - they don't have a corresponding BlockBodyOptionsType
  if (itemBlock.type === 'NEXTSECTION') {
    return null;
  }

  return blockBodyOptions.find((option) => option.value === itemBlock.type) || blockBodyOptions[0]!;
};

export const getCurrentSection = (blocks: ItemBlock[], index: number): number => {
  // หาค่า section ของ block ที่ตำแหน่ง index
  const currentBlock = blocks[index];
  if (currentBlock) {
    return currentBlock.section;
  }

  // ถ้าไม่เจอ block ที่ index นั้น ให้ย้อนหาจาก block ก่อนหน้า
  for (let i = index - 1; i >= 0; i--) {
    const block = blocks[i];
    if (block) {
      return block.section;
    }
  }

  // ถ้าไม่เจอเลย ให้คืนค่า section 1
  return 1;
};

/**
 * การคัดลอก block แบบ atomic (แนะนำให้ใช้วิธีนี้)
 * เรียก API ฝั่ง backend เพียงครั้งเดียว ลดปัญหา race condition
 */
export const duplicateBlockAtomic = async (
  sourceBlockId: number,
  duplicateData: { assessmentId: number; sequence?: number; section?: number },
  assessmentService: {
    duplicateBlock: (
      sourceBlockId: number,
      duplicateData: { assessmentId: number; sequence?: number; section?: number },
    ) => Promise<ItemBlock | undefined>;
  },
): Promise<ItemBlock | undefined> => {
  try {
    console.log('🔄 เริ่มต้นการคัดลอก block แบบ atomic:', {
      sourceBlockId,
      duplicateData,
      timestamp: new Date().toISOString(),
    });

    // เรียก API duplication แบบ atomic
    const duplicatedBlock = await assessmentService.duplicateBlock(sourceBlockId, duplicateData);

    if (duplicatedBlock) {
      console.log('✅ คัดลอก block แบบ atomic สำเร็จ:', {
        sourceBlockId,
        duplicatedBlockId: duplicatedBlock.id,
        hasContent: !!(
          duplicatedBlock.headerBody ||
          duplicatedBlock.imageBody ||
          (duplicatedBlock.questions && duplicatedBlock.questions.length > 0) ||
          (duplicatedBlock.options && duplicatedBlock.options.length > 0)
        ),
        timestamp: new Date().toISOString(),
      });

      return duplicatedBlock;
    } else {
      console.error('❌ คัดลอก block แบบ atomic ล้มเหลว: ไม่ได้ block กลับมาจาก backend');
      return undefined;
    }
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดระหว่างคัดลอก block แบบ atomic:', error);
    throw error;
  }
};

/**
 * (เลิกใช้) การคัดลอก block แบบ frontend-only (legacy)
 * - เพิ่ม delay เพื่อรอ backend ทำงานให้เสร็จ ลดปัญหา race condition
 * - deep clone เพื่อป้องกันการ share reference
 * - แนะนำให้เปลี่ยนไปใช้ duplicateBlockAtomic
 *
 * @deprecated ควรใช้ duplicateBlockAtomic แทน
 */
export const copyBlockContentWithBackendPersistence = async (
  source: ItemBlock,
  target: ItemBlock,
): Promise<ItemBlock> => {
  try {
    // import OptionService สำหรับสร้าง options แบบ persistence
    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // รอ backend สร้าง default options ให้เสร็จก่อน (ป้องกัน race condition)
    await new Promise((resolve) => setTimeout(resolve, 300));

    // deep clone target block เพื่อป้องกันการแก้ไข reference เดิม
    const updatedTarget = structuredClone
      ? structuredClone(target)
      : JSON.parse(JSON.stringify(target));

    // ถ้าเป็น ImageBlock ให้คัดลอก imageBody
    if (source.type === 'IMAGE' && source.imageBody && target.imageBody) {
      try {
        // import ImageBodyService เฉพาะตอนจำเป็น
        const { ImageBodyService } = await import('src/services/asm/imageBodyService');
        const imageBodyService = new ImageBodyService();

        // ดึง path แบบ relative จาก imagePath
        let imagePathToUse = source.imageBody.imagePath;
        if (imagePathToUse && imagePathToUse.includes('uploaded_files/')) {
          const match = imagePathToUse.match(/uploaded_files\/[^?]+/);
          if (match) {
            imagePathToUse = match[0];
          }
        }

        // อัปเดต imageBody ของ target ด้วยข้อมูลจาก source
        const updatedImageBody = await imageBodyService.updateImageBody(target.imageBody.id, {
          imagePath: imagePathToUse ?? '',
          imageText: source.imageBody.imageText || '',
          ...(source.imageBody.imageWidth !== undefined
            ? { imageWidth: source.imageBody.imageWidth }
            : {}),
          ...(source.imageBody.imageHeight !== undefined
            ? { imageHeight: source.imageBody.imageHeight }
            : {}),
          itemBlockId: target.id,
        });

        if (updatedImageBody) {
          updatedTarget.imageBody = updatedImageBody;
        }
      } catch {
        // ถ้าคัดลอก imageBody ไม่สำเร็จ ให้ข้ามไปทำส่วนอื่นต่อ
      }
    }

    // ถ้า source มี questions ให้คัดลอกและอัปเดตผ่าน backend
    if (source.questions && source.questions.length > 0 && updatedTarget.questions) {
      const updatedQuestions = [];

      for (let index = 0; index < updatedTarget.questions.length; index++) {
        const targetQuestion = updatedTarget.questions[index];
        const sourceQuestion = source.questions[index];

        if (sourceQuestion && targetQuestion) {
          try {
            // import QuestionService เฉพาะตอนจำเป็น
            const questionServiceModule = await import('src/services/asm/questionService');
            const questionService = questionServiceModule.default;

            // ดึง path แบบ relative จาก imagePath ถ้ามี
            let imagePathToUse = sourceQuestion.imagePath;
            if (imagePathToUse && imagePathToUse.includes('uploaded_files/')) {
              const match = imagePathToUse.match(/uploaded_files\/[^?]+/);
              if (match) {
                imagePathToUse = match[0];
              }
            }

            // เตรียมข้อมูลสำหรับอัปเดต question
            const questionUpdateData = {
              questionText: sourceQuestion.questionText || '',
              isHeader: sourceQuestion.isHeader,
              sequence: sourceQuestion.sequence,
              score: sourceQuestion.score || 0,
              itemBlockId: target.id,
              ...(imagePathToUse && { imagePath: imagePathToUse }),
              ...(sourceQuestion.imageWidth && { imageWidth: sourceQuestion.imageWidth }),
              ...(sourceQuestion.imageHeight && { imageHeight: sourceQuestion.imageHeight }),
              ...(sourceQuestion.sizeLimit && { sizeLimit: sourceQuestion.sizeLimit }),
              ...(sourceQuestion.acceptFile && { acceptFile: sourceQuestion.acceptFile }),
              ...(sourceQuestion.uploadLimit && { uploadLimit: sourceQuestion.uploadLimit }),
            };

            const updatedQuestion = await questionService.updateQuestion(
              targetQuestion.id,
              questionUpdateData,
            );

            if (updatedQuestion) {
              updatedQuestions.push(updatedQuestion);
            }
          } catch {
            // ถ้าอัปเดต question ไม่สำเร็จ ให้ใช้ของเดิม
            updatedQuestions.push(targetQuestion);
          }
        } else if (targetQuestion) {
          // ถ้าไม่มี source question ตรง index ให้ใช้ของเดิม
          updatedQuestions.push(targetQuestion);
        }
      }

      // อัปเดต questions ใน target
      if (updatedQuestions.length > 0) {
        updatedTarget.questions = updatedQuestions;
      }
    }

    // ถ้า source มี options ให้คัดลอกและสร้างใหม่ผ่าน backend
    if (source.options && source.options.length > 0) {
      const createdOptions: Option[] = [];

      // ลบ options เดิมทั้งหมดก่อนสร้างใหม่
      if (target.options && target.options.length > 0) {
        const deletionPromises = target.options.map(async (existingOption) => {
          try {
            await optionService.removeOption(existingOption.id);
            return { success: true, id: existingOption.id };
          } catch {
            return { success: false, id: existingOption.id };
          }
        });

        await Promise.all(deletionPromises);
        updatedTarget.options = [];
      }

      // รอให้ backend ลบ option เดิมเสร็จก่อน
      await new Promise((resolve) => setTimeout(resolve, 100));

      // สร้าง options ใหม่ตาม source
      for (let i = 0; i < source.options.length; i++) {
        const sourceOption = source.options[i];
        if (!sourceOption) {
          continue;
        }

        try {
          const newOptionData = {
            optionText: sourceOption.optionText || '',
            value: sourceOption.value || 0,
            sequence: sourceOption.sequence || i + 1,
            itemBlockId: target.id,
            ...(sourceOption.imagePath && { imagePath: sourceOption.imagePath }),
            ...(sourceOption.nextSection && { nextSection: sourceOption.nextSection }),
          };

          const createdOption = await optionService.createOption(newOptionData);

          if (createdOption) {
            createdOptions.push(createdOption);
          }
        } catch {
          // ถ้าสร้าง option ไม่สำเร็จ ให้ข้ามไปสร้างตัวถัดไป
        }
      }

      // ตรวจสอบจำนวน options ที่สร้างได้
      updatedTarget.options = createdOptions;
      if (createdOptions.length !== source.options.length) {
        console.error('❌ จำนวน options ที่สร้างไม่ตรงกับต้นฉบับ', {
          sourceCount: source.options.length,
          createdCount: createdOptions.length,
        });
      }

      // ตรวจสอบว่าไม่มี id ซ้ำใน options
      if (updatedTarget.options && updatedTarget.options.length > 0) {
        const uniqueIds = new Set(updatedTarget.options.map((opt: Option) => opt.id));
        if (uniqueIds.size !== updatedTarget.options.length) {
          console.error('❌ พบ option id ซ้ำใน block ที่คัดลอก!', {
            totalOptions: updatedTarget.options.length,
            uniqueIds: uniqueIds.size,
            optionIds: updatedTarget.options.map((opt: Option) => opt.id),
          });
        }
      }
    }

    return updatedTarget;
  } catch {
    // ถ้าคัดลอกไม่สำเร็จ ให้คืน target เดิม
    return target;
  }
};
