export type DataResponse<T> = {
  data: Array<T>;
  total: number;
  curPage: number;
  hasPrev: boolean;
  hasNext: boolean;
};

export type DataParams = {
  sortBy: string | undefined | null;
  order: 'ASC' | 'DESC';
  limit: number;
  page: number;
  search: string | undefined | null;
};

export type DevPlanTabs =
  | 'ทั้งหมด'
  | 'ทั่วไปบุคลากร'
  | 'ทั่วไปผู้บริหาร'
  | 'เฉพาะทางสายวิชาการ'
  | 'เฉพาะทางสนับสนุนวิชาการ'
  | 'เฉพาะทางผู้บริหาร'
  | 'ตำแหน่ง';

export const DevPlanTabsValues: DevPlanTabs[] = [
  'ทั้งหมด',
  'ทั่วไปบุคลากร',
  'ทั่วไปผู้บริหาร',
  'เฉพาะทางสายวิชาการ',
  'เฉพาะทางสนับสนุนวิชาการ',
  'เฉพาะทางผู้บริหาร',
  'ตำแหน่ง',
];

export type DevelopmentPlanParams = DataParams & {
  type: DevPlanTabs;
};
