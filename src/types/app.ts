import type { DropdownItemBlockType, RoleEnum } from './models';

export type System = {
  id: number;
  path: string;
  icon: string;
  nameEn: string;
  name: string;
  listRoleVisible: RoleEnum[];
};

export interface MenuLink {
  title: string;
  icon?: string;
  link: string;
  perId?: number[];
}

export interface BlockBodyOptionsType {
  label: string;
  value: DropdownItemBlockType;
  icon: string;
}

export type MenuType = 'ums' | 'quiz' | 'form' | 'competency' | 'skill' | 'idp' | 'in-house' | 'developments' | 'default';

export interface MenuGroup {
  type: MenuType;
  items: MenuLink[];
}

export interface SystemGroup {
  type: MenuType;
  systems: System[];
}
