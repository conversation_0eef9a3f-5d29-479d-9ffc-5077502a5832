export type DataResponse<T> = {
  data: Array<T>;
  total: number;
  curPage: number;
  hasPrev: boolean;
  hasNext: boolean;
};

export type DataParams = {
  sortBy: string | undefined | null;
  order: 'ASC' | 'DESC';
  limit: number;
  page: number;
  search: string | undefined | null;
};

export type AssessmentType = 'quiz' | 'evaluate';

export type AssessmentQueryParams = DataParams & {
  type: AssessmentType;
};

export type SkillType =
  | 'ความรู้และทักษะทั่วไปของบุคลากร'
  | 'ความรู้และทักษะทั่วไปของผู้บริหาร'
  | 'ความรู้และทักษะเฉพาะด้านของสายวิชาการ'
  | 'ความรู้และทักษะเฉพาะด้านของสายสนับสนุนวิชาการ'
  | 'ความรู้และทักษะเฉพาะด้านของผู้บริหาร';
export type SkillQueryParams = DataParams & {
  career_type: SkillType;
};

export type CompetencyType = 'สมรรถนะหลัก' | 'ประจำสายงาน' | 'ทางการบริหาร';
export type CareerType = 'วิชาการ' | 'สนับสนุน';

export type CompetencyQueryParams = DataParams & {
  comp_type?: CompetencyType;
  career_type?: CareerType;
};
