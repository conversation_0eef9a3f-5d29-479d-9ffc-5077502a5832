import { DataSource } from 'typeorm';
import { User } from '../../resources/users/entities/user.entity';
import { Role } from '../../resources/roles/entities/role.entity';
import { Permission } from '../../resources/permissions/entities/permission.entity';
import * as bcrypt from 'bcrypt';

export async function seedSuperAdmin(dataSource: DataSource) {
  const userRepository = dataSource.getRepository(User);
  const roleRepository = dataSource.getRepository(Role);
  const permissionRepository = dataSource.getRepository(Permission);

  console.log('Starting superadmin seeding...');

  // Get superadmin credentials from environment variables
  const superAdminEmail = process.env.SUPERADMIN_EMAIL || 'superadmin';
  const superAdminPassword = process.env.SUPERADMIN_PASSWORD || '1234';

  // Check if superadmin already exists
  const existingSuperAdmin = await userRepository.findOne({
    where: { email: superAdminEmail },
    relations: ['roles']
  });

  if (existingSuperAdmin) {
    console.log(`SuperAdmin already exists with email: ${superAdminEmail}`);
    return;
  }

  // Create super_operator permission
  let superOperatorPermission = await permissionRepository.findOne({
    where: { name: 'super_operator' }
  });

  if (!superOperatorPermission) {
    superOperatorPermission = permissionRepository.create({
      name: 'super_operator',
      descEn: 'Super Operator',
      status: true,
      isDefault: false
    });
    await permissionRepository.save(superOperatorPermission);
    console.log('Created super_operator permission');
  }

  // Create superadmin role
  let superAdminRole = await roleRepository.findOne({
    where: { name: 'Super Admin' },
    relations: ['permissions']
  });

  if (!superAdminRole) {
    superAdminRole = roleRepository.create({
      name: 'Super Admin',
      description: 'Super Administrator with all permissions'
    });
    await roleRepository.save(superAdminRole);
    console.log('Created Super Admin role');
  }

  // Assign super_operator permission to superadmin role if not already assigned
  const hasPermission = superAdminRole.permissions?.some(p => p.id === superOperatorPermission.id);
  if (!hasPermission) {
    await dataSource.query(`
      INSERT IGNORE INTO ROLES_HAS_PERMISSIONS (role_id, permission_id) 
      VALUES (?, ?)
    `, [superAdminRole.id, superOperatorPermission.id]);
    console.log('Assigned super_operator permission to superadmin role');
  }

  // Create superadmin user
  const hashedPassword = await bcrypt.hash(superAdminPassword, 10);
  
  const superAdminUser = userRepository.create({
    name: 'Super Administrator',
    email: superAdminEmail,
    password: hashedPassword
  });

  await userRepository.save(superAdminUser);
  console.log(`Created superadmin user with email: ${superAdminEmail}`);

  // Assign superadmin role to user
  await dataSource.query(`
    INSERT IGNORE INTO USER_HAS_ROLES (user_id, role_id) 
    VALUES (?, ?)
  `, [superAdminUser.id, superAdminRole.id]);
  console.log('Assigned superadmin role to user');

  console.log('SuperAdmin seeding completed successfully!');
  console.log('Login credentials:');
  console.log(`Email: ${superAdminEmail}`);
  console.log('Password: [From SUPERADMIN_PASSWORD environment variable]');
}
