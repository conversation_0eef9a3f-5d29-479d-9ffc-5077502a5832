import { DataSource } from 'typeorm';
import { User } from '../../resources/users/entities/user.entity';
import { Role } from '../../resources/roles/entities/role.entity';
import { Permission } from '../../resources/permissions/entities/permission.entity';

export async function cleanSuperAdmin(dataSource: DataSource) {
  const userRepository = dataSource.getRepository(User);
  const roleRepository = dataSource.getRepository(Role);
  const permissionRepository = dataSource.getRepository(Permission);

  console.log('Starting superadmin cleanup...');

  // Get superadmin credentials from environment variables
  const superAdminEmail = process.env.SUPERADMIN_EMAIL || 'superadmin';

  // Find and delete existing superadmin user
  const existingSuperAdmin = await userRepository.findOne({
    where: { email: superAdminEmail },
    relations: ['roles']
  });

  if (existingSuperAdmin) {
    // Delete user roles relationship
    await dataSource.query(`
      DELETE FROM USER_HAS_ROLES WHERE user_id = ?
    `, [existingSuperAdmin.id]);
    
    // Delete the user
    await userRepository.remove(existingSuperAdmin);
    console.log(`Deleted existing superadmin user: ${superAdminEmail}`);
  } else {
    console.log(`No existing superadmin user found with email: ${superAdminEmail}`);
  }

  // Find and delete superadmin role if it exists
  const existingSuperAdminRole = await roleRepository.findOne({
    where: { name: 'superadmin' },
    relations: ['permissions']
  });

  if (existingSuperAdminRole) {
    // Delete role permissions relationship
    await dataSource.query(`
      DELETE FROM ROLES_HAS_PERMISSIONS WHERE role_id = ?
    `, [existingSuperAdminRole.id]);
    
    // Delete the role
    await roleRepository.remove(existingSuperAdminRole);
    console.log('Deleted existing superadmin role');
  }

  // Find and delete super_operator permission if it exists
  const existingSuperOperatorPermission = await permissionRepository.findOne({
    where: { name: 'super_operator' }
  });

  if (existingSuperOperatorPermission) {
    await permissionRepository.remove(existingSuperOperatorPermission);
    console.log('Deleted existing super_operator permission');
  }

  console.log('SuperAdmin cleanup completed!');
}
