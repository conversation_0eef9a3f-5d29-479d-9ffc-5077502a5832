import { Injectable } from '@nestjs/common';
import { CreatePositionDto } from './dto/create-position.dto';
import { UpdatePositionDto } from './dto/update-position.dto';
import { EntityManager } from 'typeorm';
import { Position } from './entities/position.entity';

@Injectable()
export class PositionService {
  constructor(private readonly entityManager: EntityManager) {}

  async findAll() {
    const repoPosition = this.entityManager.getRepository(Position);
    const positions = await repoPosition.find();
    return positions;
  }

  findOne(id: number) {
    const repoPosition = this.entityManager.getRepository(Position);
    return repoPosition.findOneOrFail({ where: { id } });
  }
}
