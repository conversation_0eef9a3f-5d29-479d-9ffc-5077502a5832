
import { TypePlan } from 'src/resources/individual-develop-plans/type-plans/entities/type-plan.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

@Entity('HRD_POSITIONS')
export class Position {
    @PrimaryGeneratedColumn({ name: 'POSITION_ID' })
    id: number;

    @Column({ name: 'POSITION_NAME' })
    name: string;

    @OneToMany(() => TypePlan, (typePlan) => typePlan.position)
    typePlans: TypePlan
}
