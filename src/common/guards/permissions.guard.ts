import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';
import { User } from '../../resources/users/entities/user.entity';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: User = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Check if user has super_operator permission (grants access to everything)
    // Check both psnPermissions array and faculty role-based permissions
    const hasSuperOperatorInPsnPermissions = (user as any).psnPermissions?.some(
      (permission: any) => permission.perName === 'super_operator'
    );
    
    // Check faculty role-based permissions through the new structure
    const hasSuperOperatorInFacultyRoles = user.facultyUsers?.some(facultyUser =>
      facultyUser.facultyUserRoles?.some(facultyUserRole =>
        facultyUserRole.role?.permissions?.some(permission => permission.name === 'super_operator')
      )
    );

    if (hasSuperOperatorInPsnPermissions || hasSuperOperatorInFacultyRoles) {
      return true;
    }

    // Check if user has any of the required permissions
    // First check psnPermissions array
    const psnPermissionNames = (user as any).psnPermissions?.map(
      (permission: any) => permission.perName
    ) || [];
    
    // Then check faculty role-based permissions through the new structure
    const facultyRolePermissionNames = user.facultyUsers?.flatMap(facultyUser =>
      facultyUser.facultyUserRoles?.flatMap(facultyUserRole =>
        facultyUserRole.role?.permissions?.map(permission => permission.name) || []
      ) || []
    ) || [];
    
    // Combine both permission sources
    const allUserPermissions = [...psnPermissionNames, ...facultyRolePermissionNames];

    const hasPermission = requiredPermissions.some(permission =>
      allUserPermissions.includes(permission)
    );

    if (!hasPermission) {
      throw new ForbiddenException(
        `Access denied. Required permissions: ${requiredPermissions.join(', ')}`
      );
    }

    return true;
  }
}
